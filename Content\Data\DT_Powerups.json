[{"Name": "SpeedBoost_Basic", "RowStruct": "/Script/SnakeRogue.PowerupData", "PowerupID": "SPD_001", "Family": "SpeedUp", "TriggerType": "Instant", "Duration": 5.0, "Magnitude": 1.5, "DisplayName": "Speed Boost", "Description": "Increases movement speed by 50%"}, {"Name": "SpeedBoost_Advanced", "PowerupID": "SPD_002", "Family": "SpeedUp", "TriggerType": "PlayerTriggered", "Duration": 8.0, "Magnitude": 2.0, "DisplayName": "Turbo Boost", "Description": "Doubles movement speed"}, {"Name": "SlowMotion_Basic", "PowerupID": "SLO_001", "Family": "SlowMotion", "TriggerType": "PlayerTriggered", "Duration": 6.0, "Magnitude": 0.5, "DisplayName": "Bullet Time", "Description": "Slows down time by 50%"}, {"Name": "FireShield", "PowerupID": "ELE_001", "Family": "Elemental", "TriggerType": "Instant", "Duration": 10.0, "Magnitude": 1.0, "DisplayName": "Fire Shield", "Description": "Immunity to fire damage"}, {"Name": "IceArmor", "PowerupID": "ELE_002", "Family": "Elemental", "TriggerType": "Instant", "Duration": 12.0, "Magnitude": 1.0, "DisplayName": "Ice Armor", "Description": "Slows enemies on contact"}, {"Name": "EnergyShield", "PowerupID": "SHI_001", "Family": "Shield", "TriggerType": "Instant", "Duration": 15.0, "Magnitude": 3.0, "DisplayName": "Energy Shield", "Description": "Absorbs 3 hits"}, {"Name": "DoubleDamage", "PowerupID": "DMG_001", "Family": "Damage", "TriggerType": "PlayerTriggered", "Duration": 7.0, "Magnitude": 2.0, "DisplayName": "Double Damage", "Description": "Doubles all damage output"}, {"Name": "Teleport", "PowerupID": "UTI_001", "Family": "Utility", "TriggerType": "PlayerTriggered", "Duration": 0.0, "Magnitude": 1.0, "DisplayName": "Teleport", "Description": "Instantly teleport to safety"}, {"Name": "Invincibility", "PowerupID": "SPE_001", "Family": "Special", "TriggerType": "Instant", "Duration": 3.0, "Magnitude": 1.0, "DisplayName": "Invincibility", "Description": "Complete immunity to damage"}, {"Name": "ChaosMode", "PowerupID": "CHA_001", "Family": "Chaos", "TriggerType": "EnvironmentTriggered", "Duration": 20.0, "Magnitude": 1.0, "DisplayName": "Chaos Mode", "Description": "Random effects every 2 seconds"}]