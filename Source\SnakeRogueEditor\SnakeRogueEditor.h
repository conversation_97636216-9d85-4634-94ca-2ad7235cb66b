// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Framework/Commands/UICommandList.h"

DECLARE_LOG_CATEGORY_EXTERN(LogSnakeRogueEditor, Log, All);

/**
 * Snake Rogue Editor Module
 * 
 * Editor-specific functionality for the Snake Rogue game.
 * Handles custom editor tools, asset validation, and development workflow support.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
class FSnakeRogueEditorModule : public IModuleInterface
{
public:
	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

	/**
	 * Check if the editor module is loaded and ready
	 * @return true if module is loaded, false otherwise
	 */
	static bool IsModuleLoaded();

	/**
	 * Get the Snake Rogue editor module instance
	 * @return Reference to the module instance
	 */
	static FSnakeRogueEditorModule& Get();

private:
	/** Flag indicating if module has been initialized */
	bool bIsInitialized = false;

	/** Command list for custom editor actions */
	TSharedPtr<FUICommandList> CommandList;

	/** Initialize editor tools and customizations */
	void InitializeEditorTools();

	/** Shutdown editor tools and customizations */
	void ShutdownEditorTools();

	/** Register custom asset types and factories */
	void RegisterAssetTypes();

	/** Unregister custom asset types and factories */
	void UnregisterAssetTypes();

	/** Register property customizations */
	void RegisterPropertyCustomizations();

	/** Unregister property customizations */
	void UnregisterPropertyCustomizations();

	/** Register editor menu extensions */
	void RegisterMenuExtensions();

	/** Unregister editor menu extensions */
	void UnregisterMenuExtensions();

	/** Register automation tests for editor functionality */
	void RegisterEditorAutomationTests();

	/** Unregister automation tests */
	void UnregisterEditorAutomationTests();
};
