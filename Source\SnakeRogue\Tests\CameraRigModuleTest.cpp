// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "CameraRigActor.h"

#if WITH_AUTOMATION_TESTS

/**
 * Test_CameraRigModule_ShouldInitializeCorrectly
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the CameraRig module initializes correctly with dynamic tether-style camera.
 * Following TDD Red-Green-Refactor cycle:
 * 1. RED: Write failing test that describes desired behavior
 * 2. GREEN: Write minimal code to make test pass
 * 3. REFACTOR: Clean up code while maintaining green tests
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_CameraRigModule_ShouldInitializeCorrectly, 
    "SnakeRogue.CameraRig.InitializeCorrectly",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_CameraRigModule_ShouldInitializeCorrectly::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT: Create and get CameraRig module (this should now pass)
    ACameraRigActor* CameraRig = TestWorld->SpawnActor<ACameraRigActor>();
    TestTrue("CameraRig should be spawned", CameraRig != nullptr);

    if (CameraRig)
    {
        // ASSERT: Verify CameraRig module behavior
        // This test should now PASS because CameraRig module is implemented
        TestTrue("CameraRig should exist", CameraRig != nullptr);

        // Test component existence
        TestTrue("Spring arm component should exist", CameraRig->GetSpringArmComponent() != nullptr);
        TestTrue("Camera component should exist", CameraRig->GetCameraComponent() != nullptr);

        // Test initialization
        TestTrue("CameraRig should be initialized", CameraRig->IsCameraRigInitialized());

        // Test singleton behavior
        ACameraRigActor* SecondInstance = ACameraRigActor::GetCameraRig(TestWorld);
        TestEqual("CameraRig should be singleton", CameraRig, SecondInstance);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_CameraRigModule_ShouldHandleSpringArmTether
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the CameraRig module can handle spring-arm tether physics (F=kx-dv).
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_CameraRigModule_ShouldHandleSpringArmTether, 
    "SnakeRogue.CameraRig.HandleSpringArmTether",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_CameraRigModule_ShouldHandleSpringArmTether::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test spring-arm tether physics
    // This should now pass because CameraRig module is implemented
    ACameraRigActor* CameraRig = TestWorld->SpawnActor<ACameraRigActor>();
    TestTrue("CameraRig should exist for tether testing", CameraRig != nullptr);

    if (CameraRig)
    {
        // Test spring arm component configuration
        USpringArmComponent* SpringArm = CameraRig->GetSpringArmComponent();
        TestTrue("Spring arm should exist", SpringArm != nullptr);

        if (SpringArm)
        {
            // Test spring arm configuration for tether physics
            TestFalse("Spring arm should not use pawn control rotation", SpringArm->bUsePawnControlRotation);
            TestFalse("Spring arm should not inherit pitch", SpringArm->bInheritPitch);
            TestFalse("Spring arm should not inherit yaw", SpringArm->bInheritYaw);
            TestFalse("Spring arm should not inherit roll", SpringArm->bInheritRoll);
            TestTrue("Spring arm should do collision test", SpringArm->bDoCollisionTest);
        }

        // Test follow target functionality
        AActor* DummyTarget = TestWorld->SpawnActor<AActor>();
        CameraRig->SetFollowTarget(DummyTarget);
        // Note: Full physics testing would require multiple frames/ticks
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_CameraRigModule_ShouldHandleImpulsePhysics
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the CameraRig module can handle collision and boost impulse physics.
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_CameraRigModule_ShouldHandleImpulsePhysics, 
    "SnakeRogue.CameraRig.HandleImpulsePhysics",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_CameraRigModule_ShouldHandleImpulsePhysics::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test impulse physics
    // This should now pass because CameraRig module is implemented
    ACameraRigActor* CameraRig = TestWorld->SpawnActor<ACameraRigActor>();
    TestTrue("CameraRig should exist for impulse testing", CameraRig != nullptr);

    if (CameraRig)
    {
        // Store initial position
        FVector InitialPosition = CameraRig->GetActorLocation();

        // Test different impulse types
        CameraRig->ApplyCameraImpulse(ECameraImpulseType::Collision, 100.0f);
        CameraRig->ApplyCameraImpulse(ECameraImpulseType::Boost, 50.0f);
        CameraRig->ApplyCameraImpulse(ECameraImpulseType::PowerupPickup, 25.0f);
        CameraRig->ApplyCameraImpulse(ECameraImpulseType::EnemyHit, 150.0f);
        CameraRig->ApplyCameraImpulse(ECameraImpulseType::ArenaExpansion, 30.0f);

        // Test impulse with specific direction
        FVector ImpulseDirection = FVector(1.0f, 0.0f, 0.0f);
        CameraRig->ApplyCameraImpulse(ECameraImpulseType::Collision, 100.0f, ImpulseDirection);

        // Note: Position changes would be tested with Tick() calls in a full integration test
        TestTrue("CameraRig should handle impulse physics", true);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_CameraRigModule_ShouldHandleFOVZoom
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the CameraRig module can handle FOV zoom system (+10% on speed buffs).
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_CameraRigModule_ShouldHandleFOVZoom, 
    "SnakeRogue.CameraRig.HandleFOVZoom",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_CameraRigModule_ShouldHandleFOVZoom::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test FOV zoom system
    // This should now pass because CameraRig module is implemented
    ACameraRigActor* CameraRig = TestWorld->SpawnActor<ACameraRigActor>();
    TestTrue("CameraRig should exist for FOV testing", CameraRig != nullptr);

    if (CameraRig)
    {
        UCameraComponent* Camera = CameraRig->GetCameraComponent();
        TestTrue("Camera component should exist", Camera != nullptr);

        if (Camera)
        {
            // Test base FOV
            float BaseFOV = Camera->FieldOfView;
            TestTrue("Base FOV should be reasonable", BaseFOV > 30.0f && BaseFOV < 120.0f);

            // Test FOV zoom increase (+10% for speed buffs)
            CameraRig->SetFOVZoom(1.1f, false); // Immediate transition
            float ZoomedFOV = Camera->FieldOfView;
            TestTrue("FOV should increase with zoom", ZoomedFOV > BaseFOV);

            // Test FOV zoom reset
            CameraRig->SetFOVZoom(1.0f, false); // Reset to normal
            float ResetFOV = Camera->FieldOfView;
            TestEqual("FOV should reset to base", ResetFOV, BaseFOV, 0.1f);

            // Test smooth transition (would need multiple frames to fully test)
            CameraRig->SetFOVZoom(1.2f, true); // Smooth transition
            TestTrue("CameraRig should handle FOV zoom", true);
        }
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

#endif // WITH_AUTOMATION_TESTS
