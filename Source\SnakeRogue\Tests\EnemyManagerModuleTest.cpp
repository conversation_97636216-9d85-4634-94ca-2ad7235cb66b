// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "EnemyManagerActor.h"

#if WITH_AUTOMATION_TESTS

/**
 * Test_EnemyManagerModule_ShouldInitializeCorrectly
 * 
 * RED PHASE: This test should FAIL initially
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_EnemyManagerModule_ShouldInitializeCorrectly, 
    "SnakeRogue.EnemyManager.InitializeCorrectly",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_EnemyManagerModule_ShouldInitializeCorrectly::RunTest(const FString& Parameters)
{
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // Create and test EnemyManager module (this should now pass)
    AEnemyManagerActor* EnemyManager = TestWorld->SpawnActor<AEnemyManagerActor>();
    TestTrue("EnemyManager should exist", EnemyManager != nullptr);
    TestTrue("EnemyManager should be initialized", EnemyManager->IsEnemyManagerInitialized());

    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * Test_EnemyManagerModule_ShouldSpawnWallTurrets
 * 
 * RED PHASE: This test should FAIL initially
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_EnemyManagerModule_ShouldSpawnWallTurrets, 
    "SnakeRogue.EnemyManager.SpawnWallTurrets",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_EnemyManagerModule_ShouldSpawnWallTurrets::RunTest(const FString& Parameters)
{
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    AEnemyManagerActor* EnemyManager = TestWorld->SpawnActor<AEnemyManagerActor>();
    TestTrue("EnemyManager should exist for turret spawning", EnemyManager != nullptr);

    if (EnemyManager)
    {
        int32 InitialEnemyCount = EnemyManager->GetActiveEnemies().Num();
        EnemyManager->SpawnWallTurrets(2);
        int32 FinalEnemyCount = EnemyManager->GetActiveEnemies().Num();
        TestTrue("Should spawn wall turrets", FinalEnemyCount > InitialEnemyCount);
    }

    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * Test_EnemyManagerModule_ShouldHandleChaserAI
 * 
 * RED PHASE: This test should FAIL initially
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_EnemyManagerModule_ShouldHandleChaserAI, 
    "SnakeRogue.EnemyManager.HandleChaserAI",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_EnemyManagerModule_ShouldHandleChaserAI::RunTest(const FString& Parameters)
{
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    AEnemyManagerActor* EnemyManager = TestWorld->SpawnActor<AEnemyManagerActor>();
    TestTrue("EnemyManager should exist for chaser AI", EnemyManager != nullptr);

    if (EnemyManager)
    {
        EnemyManager->SpawnChasers(1);
        TArray<AActor*> ActiveEnemies = EnemyManager->GetActiveEnemies();
        TestTrue("Should spawn chasers for AI testing", ActiveEnemies.Num() > 0);
    }

    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_AUTOMATION_TESTS
