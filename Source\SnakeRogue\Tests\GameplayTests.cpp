// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "GameFramework/GameModeBase.h"
#include "Core/SnakeRogueGameMode.h"
#include "Core/SnakeRoguePlayerController.h"

#if WITH_AUTOMATION_TESTS

/**
 * GAMEPLAY TEST - BASIC GAME FUNCTIONALITY
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_BasicGameplay, 
    "SnakeRogue.Gameplay.BasicFunctionality",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_BasicGameplay::RunTest(const FString& Parameters)
{
    // ARRANGE: Create test world with game mode
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT: Set up basic game mode
    ASnakeRogueGameMode* GameMode = TestWorld->SpawnActor<ASnakeRogueGameMode>();
    TestTrue("GameMode should spawn", GameMode != nullptr);

    if (GameMode)
    {
        // Test game initialization
        bool bInitialized = GameMode->InitializeGameSystems();
        TestTrue("Game systems should initialize", bInitialized);
        
        // Test game state
        TestTrue("All systems should be initialized", GameMode->AreAllSystemsInitialized());
        
        // Test starting a new game
        GameMode->StartNewGame();
        // Game should now be active (we'd need to check internal state)
    }

    // CLEANUP
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * GAMEPLAY TEST - PLAYER CONTROLLER FUNCTIONALITY
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_PlayerController, 
    "SnakeRogue.Gameplay.PlayerController",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_PlayerController::RunTest(const FString& Parameters)
{
    // ARRANGE: Create test world
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT: Spawn player controller
    ASnakeRoguePlayerController* PlayerController = TestWorld->SpawnActor<ASnakeRoguePlayerController>();
    TestTrue("PlayerController should spawn", PlayerController != nullptr);

    if (PlayerController)
    {
        // Test input handling
        PlayerController->HandleMoveUp(1.0f);
        PlayerController->HandleMoveRight(1.0f);
        PlayerController->HandleActivatePowerup();
        PlayerController->HandleSlitherSurge();
        
        // These should not crash and should handle input gracefully
        TestTrue("Input handling should work", true);
    }

    // CLEANUP
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * GAMEPLAY TEST - PERFORMANCE VALIDATION
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_GameplayPerformance, 
    "SnakeRogue.Gameplay.Performance",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_GameplayPerformance::RunTest(const FString& Parameters)
{
    // ARRANGE: Create test world
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT: Performance test - spawn multiple actors
    TArray<AActor*> TestActors;
    
    double StartTime = FPlatformTime::Seconds();
    
    // Spawn 100 test actors to simulate game load
    for (int32 i = 0; i < 100; ++i)
    {
        AActor* TestActor = TestWorld->SpawnActor<AActor>();
        if (TestActor)
        {
            TestActors.Add(TestActor);
        }
    }
    
    double EndTime = FPlatformTime::Seconds();
    double SpawnTime = EndTime - StartTime;
    
    // ASSERT: Performance should be reasonable
    TestTrue("Should spawn 100 actors", TestActors.Num() == 100);
    TestTrue("Spawn time should be under 1 second", SpawnTime < 1.0);
    
    UE_LOG(LogTemp, Log, TEXT("Spawned %d actors in %.3f seconds"), TestActors.Num(), SpawnTime);

    // CLEANUP
    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_AUTOMATION_TESTS
