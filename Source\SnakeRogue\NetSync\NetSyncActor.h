// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "Engine/NetSerialization.h"
#include "NetSyncActor.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogNetSync, Log, All);

/**
 * Network Sync Actor
 * 
 * Handles replication and rollback for multiplayer synchronization.
 * Manages co-op and versus modes with lock-step networking.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API ANetSyncActor : public AActor
{
    GENERATED_BODY()

public:
    /** Constructor */
    ANetSyncActor();

    /** AActor interface */
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    /**
     * Get the singleton instance of NetSync
     * @param World The world context
     * @return Pointer to NetSync instance, nullptr if not found
     */
    UFUNCTION(BlueprintCallable, Category = "Net Sync")
    static ANetSyncActor* GetNetSync(const UWorld* World);

    /**
     * Initialize the network synchronization system
     * @return true if initialization successful, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Net Sync")
    bool InitializeNetSync();

    /**
     * Start replication for an actor
     * @param ActorToReplicate The actor to start replicating
     * @return true if replication started successfully
     */
    UFUNCTION(BlueprintCallable, Category = "Net Sync")
    bool StartReplication(AActor* ActorToReplicate);

    /**
     * Stop replication for an actor
     * @param ActorToStop The actor to stop replicating
     * @return true if replication stopped successfully
     */
    UFUNCTION(BlueprintCallable, Category = "Net Sync")
    bool StopReplication(AActor* ActorToStop);

    /**
     * Check if net sync is initialized
     * @return true if initialized, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Net Sync")
    bool IsNetSyncInitialized() const { return bIsNetSyncInitialized; }

    /**
     * Get all replicated actors
     * @return Array of replicated actors
     */
    UFUNCTION(BlueprintCallable, Category = "Net Sync")
    TArray<AActor*> GetReplicatedActors() const { return ReplicatedActors; }

protected:
    /** Root scene component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Net Sync")
    USceneComponent* RootSceneComponent;

    /** Network update rate in Hz */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Net Sync|Settings", meta = (ClampMin = "10", ClampMax = "120"))
    int32 NetworkUpdateRate = 60;

    /** Maximum rollback frames */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Net Sync|Settings", meta = (ClampMin = "1", ClampMax = "60"))
    int32 MaxRollbackFrames = 10;

    /** Flag indicating if net sync is initialized */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Net Sync")
    bool bIsNetSyncInitialized = false;

    /** Flag indicating if this is the singleton instance */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Net Sync")
    bool bIsSingletonInstance = false;

    /** Array of replicated actors */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Net Sync")
    TArray<AActor*> ReplicatedActors;

private:
    /** Static reference to singleton instance */
    static ANetSyncActor* SingletonInstance;

    /** Network update timer */
    float NetworkUpdateTimer = 0.0f;

    /** Update network synchronization */
    void UpdateNetworkSync(float DeltaTime);

    /** Handle rollback logic */
    void HandleRollback();

    /** Validate singleton instance */
    void ValidateSingletonInstance();
};
