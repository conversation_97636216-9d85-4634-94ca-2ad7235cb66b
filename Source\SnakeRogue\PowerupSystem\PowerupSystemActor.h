// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/DataTable.h"
#include "Components/SceneComponent.h"
#include "PowerupSystemActor.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogPowerupSystem, Log, All);

/**
 * Buff Families for categorization
 */
UENUM(BlueprintType)
enum class EBuffFamily : uint8
{
    SpeedUp         UMETA(DisplayName = "Speed-Up"),
    SlowMotion      UMETA(DisplayName = "Slow-Motion"),
    Elemental       UMETA(DisplayName = "Elemental"),
    Shield          UMETA(DisplayName = "Shield"),
    Damage          UMETA(DisplayName = "Damage"),
    Utility         UMETA(DisplayName = "Utility"),
    Special         UMETA(DisplayName = "Special"),
    Chaos           UMETA(DisplayName = "Chaos")
};

/**
 * Trigger Types for buff activation
 */
UENUM(BlueprintType)
enum class EPowerupTriggerType : uint8
{
    Instant             UMETA(DisplayName = "Instant"),
    PlayerTriggered     UMETA(DisplayName = "Player Triggered"),
    EnvironmentTriggered UMETA(DisplayName = "Environment Triggered")
};

/**
 * Powerup Data Table Row Structure
 */
USTRUCT(BlueprintType)
struct FPowerupData : public FTableRowBase
{
    GENERATED_BODY()

    /** Unique ID for the powerup */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    FString PowerupID;

    /** Buff family this powerup belongs to */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    EBuffFamily Family = EBuffFamily::Utility;

    /** Effect curve for magnitude over time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    class UCurveFloat* EffectCurve = nullptr;

    /** How this powerup is triggered */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    EPowerupTriggerType TriggerType = EPowerupTriggerType::Instant;

    /** Duration in seconds (0 = permanent until removed) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    float Duration = 5.0f;

    /** Base magnitude of the effect */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    float Magnitude = 1.0f;

    /** Display name for UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    FText DisplayName;

    /** Description for UI */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup")
    FText Description;

    FPowerupData()
    {
        PowerupID = TEXT("");
        Family = EBuffFamily::Utility;
        EffectCurve = nullptr;
        TriggerType = EPowerupTriggerType::Instant;
        Duration = 5.0f;
        Magnitude = 1.0f;
        DisplayName = FText::GetEmpty();
        Description = FText::GetEmpty();
    }
};

/**
 * Active Buff Instance
 */
USTRUCT(BlueprintType)
struct FActiveBuff
{
    GENERATED_BODY()

    /** Reference to the powerup data */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Buff")
    FPowerupData PowerupData;

    /** Remaining duration */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Buff")
    float RemainingDuration = 0.0f;

    /** Current magnitude (can change over time) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Buff")
    float CurrentMagnitude = 1.0f;

    /** Stack count for this buff */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Buff")
    int32 StackCount = 1;

    /** Time when this buff was applied */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Active Buff")
    float ApplicationTime = 0.0f;

    FActiveBuff()
    {
        RemainingDuration = 0.0f;
        CurrentMagnitude = 1.0f;
        StackCount = 1;
        ApplicationTime = 0.0f;
    }
};

/**
 * Powerup System Actor
 * 
 * Data-driven buff/debuff system with 48 buffs and 32 debuffs.
 * Handles activation, stacking, conflict resolution, and duration tracking.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API APowerupSystemActor : public AActor
{
    GENERATED_BODY()

public:
    /** Constructor */
    APowerupSystemActor();

    /** AActor interface */
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    /**
     * Get the singleton instance of PowerupSystem
     * @param World The world context
     * @return Pointer to PowerupSystem instance, nullptr if not found
     */
    UFUNCTION(BlueprintCallable, Category = "Powerup System")
    static APowerupSystemActor* GetPowerupSystem(const UWorld* World);

    /**
     * Initialize the powerup system
     * @return true if initialization successful, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Powerup System")
    bool InitializePowerupSystem();

    /**
     * Apply a powerup by ID
     * @param PowerupID The ID of the powerup to apply
     * @param TargetActor The actor to apply the powerup to
     * @return true if powerup was applied successfully
     */
    UFUNCTION(BlueprintCallable, Category = "Powerup System")
    bool ApplyPowerup(const FString& PowerupID, AActor* TargetActor);

    /**
     * Remove a powerup by ID
     * @param PowerupID The ID of the powerup to remove
     * @param TargetActor The actor to remove the powerup from
     * @return true if powerup was removed successfully
     */
    UFUNCTION(BlueprintCallable, Category = "Powerup System")
    bool RemovePowerup(const FString& PowerupID, AActor* TargetActor);

    /**
     * Get all active buffs for an actor
     * @param TargetActor The actor to query
     * @return Array of active buffs
     */
    UFUNCTION(BlueprintCallable, Category = "Powerup System")
    TArray<FActiveBuff> GetActiveBuffs(AActor* TargetActor) const;

    /**
     * Check if powerup system is initialized
     * @return true if initialized, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Powerup System")
    bool IsPowerupSystemInitialized() const { return bIsPowerupSystemInitialized; }

    /**
     * Get powerup data by ID
     * @param PowerupID The ID to look up
     * @param OutPowerupData The powerup data output (if found)
     * @return true if powerup found, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Powerup System")
    bool GetPowerupData(const FString& PowerupID, FPowerupData& OutPowerupData);

protected:
    /** Root scene component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Powerup System")
    USceneComponent* RootSceneComponent;

    /** Data table for powerups (buffs) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup System|Data")
    UDataTable* PowerupsDataTable = nullptr;

    /** Data table for debuffs */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup System|Data")
    UDataTable* DebuffsDataTable = nullptr;

    /** Maximum stacks per buff family */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Powerup System|Settings", meta = (ClampMin = "1", ClampMax = "10"))
    int32 MaxStacksPerFamily = 3;

    /** Flag indicating if powerup system is initialized */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Powerup System")
    bool bIsPowerupSystemInitialized = false;

    /** Flag indicating if this is the singleton instance */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Powerup System")
    bool bIsSingletonInstance = false;

    /** Map of active buffs per actor (not exposed to Blueprint due to TArray value limitation) */
    TMap<AActor*, TArray<FActiveBuff>> ActiveBuffsMap;

private:
    /** Static reference to singleton instance */
    static APowerupSystemActor* SingletonInstance;

    /** Cached powerup data for quick lookup */
    TMap<FString, FPowerupData*> PowerupDataCache;

    /** Load data tables */
    bool LoadDataTables();

    /** Update active buffs */
    void UpdateActiveBuffs(float DeltaTime);

    /** Apply buff effect to actor */
    void ApplyBuffEffect(const FActiveBuff& Buff, AActor* TargetActor);

    /** Remove buff effect from actor */
    void RemoveBuffEffect(const FActiveBuff& Buff, AActor* TargetActor);

    /** Handle buff stacking logic */
    bool HandleBuffStacking(const FPowerupData& PowerupData, AActor* TargetActor);

    /** Resolve buff conflicts */
    void ResolveBuffConflicts(AActor* TargetActor);

    /** Validate singleton instance */
    void ValidateSingletonInstance();
};
