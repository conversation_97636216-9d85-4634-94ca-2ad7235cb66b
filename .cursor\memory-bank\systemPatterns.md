# Snake Rogue - System Patterns

## Architecture Overview

### Modular Design Pattern
The project follows a strict modular architecture where each major gameplay system is implemented as an independent C++ module with well-defined interfaces.

```
SnakeRogue (Main Module)
├── Core/               # Game mode & player controller
├── CoreLoop/           # Game state management & singleton pattern
├── CameraRig/          # Dynamic camera with spring physics
├── LevelGen/           # Procedural generation with streaming
├── PowerupSystem/      # Data-driven buff/debuff system
├── EnemyManager/       # AI behavior trees & spawning
├── NetSync/            # Deterministic networking
└── Tests/              # TDD test suite for all modules
```

### Key Design Patterns

#### 1. Singleton Pattern for System Management
- **CoreLoopGameState**: Single game state managing overall flow
- **PowerupSystemActor**: Centralized power-up management
- **Pattern**: Static `GetInstance()` methods with validation
- **Benefit**: Guarantees single source of truth for system state

#### 2. Factory Pattern for Dynamic Content
- **Power-up Creation**: JSON data tables drive runtime instantiation
- **Enemy Spawning**: Configurable spawning based on difficulty curves
- **Arena Generation**: Procedural tile factory with reuse pooling
- **Pattern**: `CreateFromData()` methods with validation

#### 3. Observer Pattern for Inter-Module Communication
- **Event System**: Loose coupling between modules
- **State Changes**: Game flow state transitions notify subscribers
- **Performance Events**: Camera shake, UI updates, sound triggers
- **Pattern**: Delegate/Event system with weak references

#### 4. Strategy Pattern for Configurable Behavior
- **Power-up Effects**: Different trigger types (Instant, PlayerTriggered)
- **Art Styles**: Swappable rendering pipelines
- **Difficulty Scaling**: Configurable enemy behavior profiles
- **Pattern**: Interface-based polymorphism with data-driven selection

## Development Patterns

### Test-Driven Development (TDD)
Every feature follows strict Red-Green-Refactor methodology:

1. **RED**: Write failing test with Arrange-Act-Assert pattern
2. **GREEN**: Implement minimal code to pass test
3. **REFACTOR**: Clean up while maintaining green tests

```cpp
// Example TDD Pattern
class Test_PowerupSystem : public FAutomationTestBase {
    // Arrange
    APowerupSystemActor* System = CreateTestSystem();
    
    // Act  
    bool Result = System->ApplyPowerup("SPD_001");
    
    // Assert
    TestTrue("Powerup applied successfully", Result);
}
```

### Epic C++ Standards Compliance
- **Naming**: PascalCase classes, bPrefixed booleans, VerbNoun() methods
- **Memory Management**: Smart pointers and UE garbage collection
- **Performance**: Frame budget awareness (8.3ms target)
- **Documentation**: Comprehensive header comments

### Module Communication Pattern
```cpp
// Loose coupling through interfaces
class IGameSystemInterface {
public:
    virtual bool Initialize() = 0;
    virtual void Shutdown() = 0;
    virtual void Tick(float DeltaTime) = 0;
};

// Event-driven updates
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnGameStateChanged, EGameFlowState, NewState);
```

## Data Patterns

### JSON-Driven Configuration
All game content is defined in JSON data tables for:
- **Power-ups**: 48 unique buffs with families and effects
- **Debuffs**: 32 penalties with duration/magnitude curves
- **Enemies**: Behavior parameters and spawn weights
- **Art Styles**: Material swaps and effect overrides

```json
{
  "PowerupID": "SPD_001",
  "Family": "SpeedUp", 
  "TriggerType": "Instant",
  "Duration": 5.0,
  "Magnitude": 1.5
}
```

### Data Validation Pipeline
- **Runtime Validation**: JSON schema enforcement
- **Unit Testing**: Every data table has corresponding test coverage
- **Hot Reload**: Development-time data changes without restart
- **Version Control**: JSON files in Git with merge-friendly formatting

## Performance Patterns

### Frame Budget Management
Total 8.3ms budget (120 FPS) allocated across systems:
- **Gameplay Logic**: 2.0ms
- **Rendering**: 3.5ms  
- **Audio**: 0.5ms
- **Networking**: 0.5ms
- **Buffer**: 1.8ms

### Memory Optimization
- **Object Pooling**: Reuse bullet/enemy/effect instances
- **Streaming**: Arena tiles load/unload based on distance
- **Smart Pointers**: Automatic cleanup with UE garbage collection
- **Data Locality**: Component-based entity organization

### Platform Scaling
```cpp
// Adaptive quality based on platform
class FPlatformSettings {
    static int32 GetTargetFPS() {
        return GEngine->IsUsingHighPerformanceGPU() ? 120 : 60;
    }
    
    static bool ShouldUseLumen() {
        return FPlatformMisc::GetGPUTier() >= 3;
    }
};
```

## Networking Patterns

### Deterministic Gameplay
- **Fixed-Point Math**: Consistent calculations across platforms
- **Input Serialization**: Commands rather than state synchronization
- **Rollback Support**: State snapshots for conflict resolution
- **Lockstep Validation**: Hash comparison for desync detection

### Multiplayer Architecture
```cpp
// Client-Server with deterministic validation
class ANetSyncActor {
    void ProcessInputCommand(const FInputCommand& Command);
    void ValidateGameState(uint32 ExpectedHash);
    void HandleDesyncDetection();
};
```

## Testing Patterns

### Automated Test Suite
- **Unit Tests**: 95% coverage for core logic
- **Integration Tests**: Cross-module communication validation
- **Performance Tests**: Frame budget compliance verification
- **Functional Tests**: End-to-end gameplay scenarios

### Continuous Integration
- **Build Validation**: Automated compilation on commit
- **Test Execution**: Full suite runs on multiple platforms
- **Coverage Reporting**: Regression detection and metrics
- **Deployment Pipeline**: Automated packaging for releases

## Error Handling Patterns

### Graceful Degradation
- **Missing Assets**: Fallback to default materials/meshes
- **Network Issues**: Offline mode transition
- **Performance Problems**: Automatic quality reduction
- **Data Corruption**: Validation with error recovery

### Logging Strategy
```cpp
// Structured logging with categories
DEFINE_LOG_CATEGORY(LogSnakeRogue);
UE_LOG(LogSnakeRogue, Warning, TEXT("System %s failed to initialize"), *SystemName);
```

## Build & Deployment Patterns

### Multi-Configuration Support
- **SnakeRogue.uproject**: Full development build
- **SnakeRogue_Clean.uproject**: Minimal feature set
- **SnakeRogue_Minimal.uproject**: Ultra-lightweight version

### Automated Scripts
- **Launch Scripts**: Different UE versions and configurations
- **Build Validation**: Pre-commit hooks and CI integration
- **Packaging**: Platform-specific deployment preparation
