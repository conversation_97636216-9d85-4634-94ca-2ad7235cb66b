// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "SnakeRoguePlayerController.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogSnakeRoguePlayerController, Log, All);

/**
 * Snake Rogue Player Controller
 * 
 * Handles input processing and player interaction with the game world.
 * Manages camera control, powerup activation, and UI interaction.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API ASnakeRoguePlayerController : public APlayerController
{
    GENERATED_BODY()

public:
    /** Constructor */
    ASnakeRoguePlayerController();

    /** APlayerController interface */
    virtual void BeginPlay() override;
    virtual void SetupInputComponent() override;
    virtual void Tick(float DeltaTime) override;

    /**
     * Handle movement input
     * @param Value Input value (-1 to 1)
     */
    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandleMoveUp(float Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandleMoveDown(float Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandleMoveLeft(float Value);

    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandleMoveRight(float Value);

    /**
     * Handle action inputs
     */
    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandleActivatePowerup();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandleSlitherSurge();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandlePause();

    UFUNCTION(BlueprintCallable, Category = "Input")
    void HandleShowScoreboard();

protected:
    /** Current movement input */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Input")
    FVector2D MovementInput = FVector2D::ZeroVector;

    /** Flag indicating if slither surge is active */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Input")
    bool bSlitherSurgeActive = false;

    /** Flag indicating if scoreboard is shown */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Input")
    bool bScoreboardShown = false;

private:
    /** Process movement input */
    void ProcessMovementInput(float DeltaTime);

    /** Update camera based on input */
    void UpdateCameraInput(float DeltaTime);
};
