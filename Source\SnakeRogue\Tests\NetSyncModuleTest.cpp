// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "NetSyncActor.h"

#if WITH_AUTOMATION_TESTS

/**
 * Test_NetSyncModule_ShouldInitializeCorrectly
 * 
 * RED PHASE: This test should FAIL initially
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_NetSyncModule_ShouldInitializeCorrectly, 
    "SnakeRogue.NetSync.InitializeCorrectly",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_NetSyncModule_ShouldInitializeCorrectly::RunTest(const FString& Parameters)
{
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    ANetSyncActor* NetSync = TestWorld->SpawnActor<ANetSyncActor>();
    TestTrue("NetSync should exist", NetSync != nullptr);
    TestTrue("NetSync should be initialized", NetSync->IsNetSyncInitialized());

    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * Test_NetSyncModule_ShouldHandleReplication
 * 
 * RED PHASE: This test should FAIL initially
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_NetSyncModule_ShouldHandleReplication, 
    "SnakeRogue.NetSync.HandleReplication",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_NetSyncModule_ShouldHandleReplication::RunTest(const FString& Parameters)
{
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    ANetSyncActor* NetSync = TestWorld->SpawnActor<ANetSyncActor>();
    TestTrue("NetSync should exist for replication", NetSync != nullptr);

    if (NetSync)
    {
        AActor* TestActor = TestWorld->SpawnActor<AActor>();
        bool bStarted = NetSync->StartReplication(TestActor);
        TestTrue("Should start replication", bStarted);

        TArray<AActor*> ReplicatedActors = NetSync->GetReplicatedActors();
        TestTrue("Should have replicated actors", ReplicatedActors.Num() > 0);
    }

    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_AUTOMATION_TESTS
