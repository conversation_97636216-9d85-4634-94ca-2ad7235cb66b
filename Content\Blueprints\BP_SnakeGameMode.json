{"BlueprintType": "GameMode", "Name": "BP_SnakeGameMode", "Description": "Main Game Mode for Snake Rogue", "DefaultPawn": "BP_SnakePlayer", "Variables": [{"Name": "CurrentScore", "Type": "Integer", "Default": 0}, {"Name": "GameState", "Type": "Enum", "Values": ["Playing", "Paused", "GameOver"], "Default": "Playing"}, {"Name": "ArenaSize", "Type": "Vector2D", "Default": [20, 20]}, {"Name": "FoodActors", "Type": "<PERSON><PERSON>y<Actor>", "Default": []}], "Events": [{"Name": "BeginPlay", "Actions": ["InitializeArena()", "SpawnInitialFood(5)", "SetupUI()", "StartGameTimer()"]}, {"Name": "OnPlayerScored", "Actions": ["AddScore(Points)", "UpdateUI()", "CheckForLevelUp()", "SpawnMoreFood()"]}, {"Name": "OnGameOver", "Actions": ["SetGameState(GameOver)", "ShowGameOverScreen()", "SaveHighScore()"]}]}