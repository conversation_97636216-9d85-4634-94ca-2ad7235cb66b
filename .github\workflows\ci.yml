name: Snake Rogue CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  UE_VERSION: "5.4"
  BUILD_CONFIGURATION: "Development"
  COVERAGE_THRESHOLD: 95

jobs:
  test:
    name: Run Tests with Coverage
    runs-on: windows-latest
    
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
      with:
        lfs: true
        
    - name: Setup Unreal Engine
      uses: game-ci/unity-builder@v2
      with:
        unityVersion: ${{ env.UE_VERSION }}
        
    - name: Cache UE Build Dependencies
      uses: actions/cache@v3
      with:
        path: |
          Binaries/
          Intermediate/
        key: ${{ runner.os }}-ue-${{ hashFiles('**/*.uproject', '**/*.Build.cs') }}
        
    - name: Build Project
      run: |
        "C:\Program Files\Epic Games\UE_${{ env.UE_VERSION }}\Engine\Build\BatchFiles\Build.bat" SnakeRogueEditor Win64 ${{ env.BUILD_CONFIGURATION }} -project="${{ github.workspace }}\SnakeRogue.uproject"
        
    - name: Run Unit Tests
      run: |
        "C:\Program Files\Epic Games\UE_${{ env.UE_VERSION }}\Engine\Binaries\Win64\UnrealEditor-Cmd.exe" "${{ github.workspace }}\SnakeRogue.uproject" -TestExit="Automation RunAll" -ReportOutputPath="${{ github.workspace }}\TestResults" -log
        
    - name: Run Low-Level Tests
      run: |
        "C:\Program Files\Epic Games\UE_${{ env.UE_VERSION }}\Engine\Binaries\Win64\LowLevelTests.exe" --reporter=junit --out="${{ github.workspace }}\TestResults\LowLevelTests.xml"
        
    - name: Generate Coverage Report
      run: |
        "C:\Program Files\Epic Games\UE_${{ env.UE_VERSION }}\Engine\Binaries\Win64\UnrealEditor-Cmd.exe" "${{ github.workspace }}\SnakeRogue.uproject" -ExecCmds="Automation RunTests Core+;quit" -log -ReportOutputPath="${{ github.workspace }}\CoverageResults"
        
    - name: Check Coverage Threshold
      run: |
        $coverageFile = "${{ github.workspace }}\CoverageResults\coverage.xml"
        if (Test-Path $coverageFile) {
          [xml]$coverage = Get-Content $coverageFile
          $lineRate = [double]$coverage.coverage.'line-rate'
          $percentage = $lineRate * 100
          Write-Host "Coverage: $percentage%"
          if ($percentage -lt ${{ env.COVERAGE_THRESHOLD }}) {
            Write-Error "Coverage $percentage% is below threshold ${{ env.COVERAGE_THRESHOLD }}%"
            exit 1
          }
        } else {
          Write-Error "Coverage report not found"
          exit 1
        }
        
    - name: Upload Test Results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results
        path: TestResults/
        
    - name: Upload Coverage Report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: coverage-report
        path: CoverageResults/
        
    - name: Validate Commit Message
      if: github.event_name == 'pull_request'
      run: |
        $commitMsg = "${{ github.event.pull_request.title }}"
        $validPrefixes = @("test:", "feat:", "refactor:", "fix:", "docs:", "chore:")
        $isValid = $false
        foreach ($prefix in $validPrefixes) {
          if ($commitMsg.StartsWith($prefix)) {
            $isValid = $true
            break
          }
        }
        if (-not $isValid) {
          Write-Error "Commit message must start with one of: $($validPrefixes -join ', ')"
          exit 1
        }

  build-windows:
    name: Build Windows
    needs: test
    runs-on: windows-latest
    
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
      with:
        lfs: true
        
    - name: Build Windows Package
      run: |
        "C:\Program Files\Epic Games\UE_${{ env.UE_VERSION }}\Engine\Build\BatchFiles\RunUAT.bat" BuildCookRun -project="${{ github.workspace }}\SnakeRogue.uproject" -platform=Win64 -configuration=${{ env.BUILD_CONFIGURATION }} -cook -package -stage -archive -archivedirectory="${{ github.workspace }}\Packages"
        
    - name: Upload Windows Build
      uses: actions/upload-artifact@v3
      with:
        name: SnakeRogue_Windows_${{ github.sha }}_${{ github.run_number }}
        path: Packages/

  build-linux:
    name: Build Linux
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
      with:
        lfs: true
        
    - name: Setup Unreal Engine Linux
      run: |
        # Setup UE5 for Linux cross-compilation
        sudo apt-get update
        sudo apt-get install -y clang-13 lld-13
        
    - name: Build Linux Package
      run: |
        /opt/UnrealEngine/Engine/Build/BatchFiles/RunUAT.sh BuildCookRun -project="${{ github.workspace }}/SnakeRogue.uproject" -platform=Linux -configuration=${{ env.BUILD_CONFIGURATION }} -cook -package -stage -archive -archivedirectory="${{ github.workspace }}/Packages"
        
    - name: Upload Linux Build
      uses: actions/upload-artifact@v3
      with:
        name: SnakeRogue_Linux_${{ github.sha }}_${{ github.run_number }}
        path: Packages/

  deploy:
    name: Deploy to itch.io
    needs: [build-windows, build-linux]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Download Windows Build
      uses: actions/download-artifact@v3
      with:
        name: SnakeRogue_Windows_${{ github.sha }}_${{ github.run_number }}
        path: ./builds/windows/
        
    - name: Download Linux Build
      uses: actions/download-artifact@v3
      with:
        name: SnakeRogue_Linux_${{ github.sha }}_${{ github.run_number }}
        path: ./builds/linux/
        
    - name: Setup Butler (itch.io CLI)
      run: |
        curl -L -o butler.zip https://broth.itch.ovh/butler/linux-amd64/LATEST/archive/default
        unzip butler.zip
        chmod +x butler
        
    - name: Deploy to itch.io
      env:
        BUTLER_API_KEY: ${{ secrets.BUTLER_API_KEY }}
      run: |
        ./butler push ./builds/windows/ rolaand-jayz/snake-rogue:windows --userversion ${{ github.run_number }}
        ./butler push ./builds/linux/ rolaand-jayz/snake-rogue:linux --userversion ${{ github.run_number }}
