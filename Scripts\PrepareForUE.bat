@echo off
echo ========================================
echo    SNAKE ROGUE - UE5.4 PREPARATION
echo ========================================
echo.

echo [INFO] Preparing project for Unreal Engine 5.4...
echo.

REM Check if project files are ready
echo [CHECK] Validating project structure...
if exist "SnakeRogue.uproject" (
    echo ✅ SnakeRogue.uproject - READY
) else (
    echo ❌ SnakeRogue.uproject - MISSING
    goto :error
)

if exist "Source\SnakeRogue" (
    echo ✅ Source directory - READY
) else (
    echo ❌ Source directory - MISSING
    goto :error
)

if exist "Config" (
    echo ✅ Config directory - READY
) else (
    echo ❌ Config directory - MISSING
    goto :error
)

echo.
echo [CHECK] Validating core modules...
set MODULE_COUNT=0

if exist "Source\SnakeRogue\CoreLoop" (
    echo ✅ CoreLoop Module - READY
    set /a MODULE_COUNT+=1
)

if exist "Source\SnakeRogue\CameraRig" (
    echo ✅ CameraRig Module - READY
    set /a MODULE_COUNT+=1
)

if exist "Source\SnakeRogue\LevelGen" (
    echo ✅ LevelGen Module - READY
    set /a MODULE_COUNT+=1
)

if exist "Source\SnakeRogue\PowerupSystem" (
    echo ✅ PowerupSystem Module - READY
    set /a MODULE_COUNT+=1
)

if exist "Source\SnakeRogue\EnemyManager" (
    echo ✅ EnemyManager Module - READY
    set /a MODULE_COUNT+=1
)

if exist "Source\SnakeRogue\NetSync" (
    echo ✅ NetSync Module - READY
    set /a MODULE_COUNT+=1
)

echo.
echo [RESULT] Found %MODULE_COUNT%/6 core modules

if %MODULE_COUNT% EQU 6 (
    echo ✅ ALL MODULES READY!
) else (
    echo ❌ MISSING MODULES!
    goto :error
)

echo.
echo [CHECK] Validating data tables...
if exist "Content\Data\DT_Powerups.json" (
    echo ✅ DT_Powerups - READY
) else (
    echo ❌ DT_Powerups - MISSING
)

if exist "Content\Data\DT_Debuffs.json" (
    echo ✅ DT_Debuffs - READY
) else (
    echo ❌ DT_Debuffs - MISSING
)

echo.
echo [CHECK] Validating test suite...
if exist "Source\SnakeRogue\Tests" (
    echo ✅ Test Directory - READY
    dir "Source\SnakeRogue\Tests\*.cpp" /b 2>nul | find /c ".cpp" > temp_count.txt
    set /p TEST_COUNT=<temp_count.txt
    del temp_count.txt
    echo ✅ Found test files
) else (
    echo ❌ Test Directory - MISSING
)

echo.
echo ========================================
echo    PREPARATION COMPLETE!
echo ========================================
echo.
echo 🎉 PROJECT IS READY FOR UNREAL ENGINE! 🎉
echo.
echo Next steps:
echo   1. ✅ Install Unreal Engine 5.4
echo   2. ✅ Double-click SnakeRogue.uproject
echo   3. ✅ Let UE generate project files
echo   4. ✅ Compile and run!
echo.
echo Expected compilation time: 5-15 minutes
echo Expected result: PERFECT SUCCESS! 🚀
echo.
goto :end

:error
echo.
echo ❌ ERROR: Project not ready for UE5.4
echo Please check the project structure.
echo.

:end
pause
