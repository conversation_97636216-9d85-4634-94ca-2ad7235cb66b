// Copyright Epic Games, Inc. All Rights Reserved.

#include "SnakeRogueEditor.h"
#include "Modules/ModuleManager.h"
#include "PropertyEditorModule.h"
#include "LevelEditor.h"
#include "Framework/Commands/UICommandList.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "ToolMenus.h"

DEFINE_LOG_CATEGORY(LogSnakeRogueEditor);

IMPLEMENT_GAME_MODULE(FSnakeRogueEditorModule, SnakeRogueEditor);

void FSnakeRogueEditorModule::StartupModule()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Snake Rogue Editor module starting up..."));
	
	// Initialize editor tools and customizations
	InitializeEditorTools();
	
	// Register custom asset types
	RegisterAssetTypes();
	
	// Register property customizations
	RegisterPropertyCustomizations();
	
	// Register menu extensions
	RegisterMenuExtensions();
	
	// Register automation tests for editor functionality
	RegisterEditorAutomationTests();
	
	bIsInitialized = true;
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Snake Rogue Editor module startup complete"));
}

void FSnakeRogueEditorModule::ShutdownModule()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Snake Rogue Editor module shutting down..."));
	
	// Unregister automation tests
	UnregisterEditorAutomationTests();
	
	// Unregister menu extensions
	UnregisterMenuExtensions();
	
	// Unregister property customizations
	UnregisterPropertyCustomizations();
	
	// Unregister custom asset types
	UnregisterAssetTypes();
	
	// Shutdown editor tools
	ShutdownEditorTools();
	
	bIsInitialized = false;
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Snake Rogue Editor module shutdown complete"));
}

bool FSnakeRogueEditorModule::IsModuleLoaded()
{
	return FModuleManager::Get().IsModuleLoaded("SnakeRogueEditor");
}

FSnakeRogueEditorModule& FSnakeRogueEditorModule::Get()
{
	return FModuleManager::LoadModuleChecked<FSnakeRogueEditorModule>("SnakeRogueEditor");
}

void FSnakeRogueEditorModule::InitializeEditorTools()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Initializing editor tools..."));
	
	// Create command list for custom actions
	CommandList = MakeShareable(new FUICommandList);
	
	// TODO: Initialize custom editor tools
	// TODO: Initialize asset validation tools
	// TODO: Initialize TDD workflow tools
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Editor tools initialized"));
}

void FSnakeRogueEditorModule::ShutdownEditorTools()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Shutting down editor tools..."));
	
	// Clear command list
	CommandList.Reset();
	
	// TODO: Shutdown custom editor tools
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Editor tools shutdown complete"));
}

void FSnakeRogueEditorModule::RegisterAssetTypes()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Registering custom asset types..."));
	
	// TODO: Register custom asset types for data tables
	// TODO: Register custom asset types for buff/debuff definitions
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Custom asset types registered"));
}

void FSnakeRogueEditorModule::UnregisterAssetTypes()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Unregistering custom asset types..."));
	
	// TODO: Unregister custom asset types
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Custom asset types unregistered"));
}

void FSnakeRogueEditorModule::RegisterPropertyCustomizations()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Registering property customizations..."));
	
	// Get property editor module
	FPropertyEditorModule& PropertyModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");
	
	// TODO: Register custom property editors for game-specific types
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Property customizations registered"));
}

void FSnakeRogueEditorModule::UnregisterPropertyCustomizations()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Unregistering property customizations..."));
	
	// Get property editor module if still loaded
	if (FModuleManager::Get().IsModuleLoaded("PropertyEditor"))
	{
		FPropertyEditorModule& PropertyModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");
		
		// TODO: Unregister custom property editors
	}
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Property customizations unregistered"));
}

void FSnakeRogueEditorModule::RegisterMenuExtensions()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Registering menu extensions..."));
	
	// TODO: Register custom menu items for TDD workflow
	// TODO: Register custom toolbar buttons for asset validation
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Menu extensions registered"));
}

void FSnakeRogueEditorModule::UnregisterMenuExtensions()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Unregistering menu extensions..."));
	
	// TODO: Unregister custom menu items
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Menu extensions unregistered"));
}

void FSnakeRogueEditorModule::RegisterEditorAutomationTests()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Registering editor automation tests..."));
	
	// TODO: Register editor-specific automation tests
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Editor automation tests registered"));
}

void FSnakeRogueEditorModule::UnregisterEditorAutomationTests()
{
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Unregistering editor automation tests..."));
	
	// TODO: Unregister editor-specific automation tests
	
	UE_LOG(LogSnakeRogueEditor, Log, TEXT("Editor automation tests unregistered"));
}
