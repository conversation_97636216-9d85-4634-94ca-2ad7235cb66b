// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "SnakeRogueGameMode.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogSnakeRogueGameMode, Log, All);

/**
 * Snake Rogue Game Mode
 * 
 * Main game mode that orchestrates all core modules.
 * Handles game initialization, state management, and module coordination.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API ASnakeRogueGameMode : public AGameModeBase
{
    GENERATED_BODY()

public:
    /** Constructor */
    ASnakeRogueGameMode();

    /** AGameModeBase interface */
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

    /**
     * Initialize all game systems
     * @return true if initialization successful
     */
    UFUNCTION(BlueprintCallable, Category = "Snake Rogue")
    bool InitializeGameSystems();

    /**
     * Start a new game
     */
    UFUNCTION(BlueprintCallable, Category = "Snake Rogue")
    void StartNewGame();

    /**
     * End the current game
     */
    UFUNCTION(BlueprintCallable, Category = "Snake Rogue")
    void EndGame();

    /**
     * Check if all systems are initialized
     * @return true if all systems ready
     */
    UFUNCTION(BlueprintCallable, Category = "Snake Rogue")
    bool AreAllSystemsInitialized() const { return bAllSystemsInitialized; }

protected:
    /** Flag indicating if all systems are initialized */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Snake Rogue")
    bool bAllSystemsInitialized = false;

    /** Flag indicating if game is currently active */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Snake Rogue")
    bool bGameActive = false;

private:
    /** Initialize core modules */
    bool InitializeCoreModules();

    /** Setup module communication */
    void SetupModuleCommunication();

    /** Validate all systems are working */
    bool ValidateAllSystems();
};
