# Debugging & Profiling Strategy – **Snake Rogue**

## 1. Logging
* Use UE `UE_LOG(LogSnake, Verbose, ...)` macros.
* Category per module (Movement, LevelGen, Buffs).

## 2. Profiling
* **Unreal Insights** for CPU/GPU.
* **GPU Visualizer** (`stat gpu`) every milestone.
* Custom CSV export for frame budget.

## 3. Common Debug Scenarios
| Symptom | Likely Cause | Tool |
|---------|-------------|------|
| Camera jitter | Weight params too low | Live Edit spring constants |
| Frame spikes | Niagara FX burst | GPU Profiler |
| Multiplayer desync | Non‑deterministic RNG | Network Profiler |

## 4. Crash Handling
* auto‑generate minidumps → upload to Sentry (free tier).
