@echo off
echo ========================================
echo    SNAKE ROGUE - QUICK PLAY TEST
echo ========================================
echo.

echo [INFO] Setting up quick play test in UE5.6...
echo.

echo TESTING CHECKLIST:
echo.
echo ✅ BASIC MOVEMENT:
echo   - WASD keys to move snake
echo   - Smooth grid-based movement
echo   - Direction changes work
echo.
echo ✅ FOOD COLLECTION:
echo   - Red spheres are food
echo   - Collect food to grow snake
echo   - Score increases on collection
echo.
echo ✅ ARENA SYSTEM:
echo   - Arena expands every 4 seconds
echo   - New tiles spawn automatically
echo   - Fog boundary visible
echo.
echo ✅ CAMERA SYSTEM:
echo   - Camera follows snake smoothly
echo   - Spring-arm physics active
echo   - FOV zoom on speed boosts
echo.
echo ✅ POWERUP SYSTEM:
echo   - Different colored pickups
echo   - Temporary effects applied
echo   - Visual feedback on activation
echo.
echo ✅ CORE MODULES:
echo   - <PERSON><PERSON><PERSON> managing game state
echo   - <PERSON><PERSON>ig handling view
echo   - LevelGen expanding arena
echo   - PowerupSystem applying effects
echo   - EnemyManager spawning hazards
echo.
echo CONTROLS:
echo   W/A/S/D - Move snake
echo   SPACE - Activate powerup
echo   ESC - Pause game
echo   R - Restart level
echo.
echo WHAT TO LOOK FOR:
echo   🐍 Snake moves smoothly
echo   🍎 Food spawns and can be collected
echo   📈 Score increases
echo   🎥 Camera follows nicely
echo   🌟 Visual effects work
echo   🎮 Game feels responsive
echo.
echo ========================================
echo    READY TO PLAY! 🎮
echo ========================================
echo.
echo 1. In UE5.6: Click PLAY button
echo 2. Use WASD to move
echo 3. Collect red food spheres
echo 4. Watch the arena expand
echo 5. Test all our systems!
echo.
echo 🚀 ENJOY TESTING OUR CREATION! 🚀
echo.
pause
