# Snake Rogue - Technical Context

## Technology Stack

### Core Engine
- **Unreal Engine 5.6** (targeting 5.4 compatibility)
- **C++ 17** with Epic Games coding standards
- **Blueprint Integration** for rapid prototyping and UI
- **Platform Support**: Windows 11, Linux, Steam Deck, Console (stretch)

### Development Tools
- **IDE**: Visual Studio 2022 with Unreal Engine integration
- **Version Control**: Git + Git LFS for binary assets
- **Build System**: UnrealBuildTool with custom configurations
- **CI/CD**: GitHub Actions for automated testing and packaging
- **Testing**: Unreal Engine Automation Framework + Custom TDD suite

### Graphics & Rendering
- **Lumen**: Enabled for high-tier platforms (RTX 3060+)
- **Nanite**: Disabled (incompatible with tile-based level generation)
- **Rendering Pipeline**: Forward rendering with custom post-processing
- **Target Resolution**: 4K @ 120 FPS (high-end), 1080p @ 60 FPS (mid-range)

### Audio System
- **Unreal Audio Engine**: Integrated spatial audio
- **Dynamic Music**: 5 audio themes matching art styles
- **Sound Effects**: Procedural audio for power-up combinations
- **Platform Audio**: Optimized for headphones and speakers

## Project Structure

### Source Organization
```
Source/
├── SnakeRogue/              # Main game module
│   ├── Core/                # Game mode, player controller
│   ├── CoreLoop/            # Game state management
│   ├── CameraRig/           # Dynamic camera system
│   ├── LevelGen/            # Procedural arena generation
│   ├── PowerupSystem/       # Buff/debuff management
│   ├── EnemyManager/        # AI and hazard systems
│   ├── NetSync/             # Multiplayer networking
│   └── Tests/               # TDD test suite
├── SnakeRogueEditor/        # Editor-only functionality
└── ThirdParty/              # External dependencies (if needed)
```

### Configuration Files
- **SnakeRogue.uproject**: Main project definition
- **SnakeRogue_Clean.uproject**: Minimal feature build
- **SnakeRogue_Minimal.uproject**: Ultra-lightweight version
- **Config/DefaultEngine.ini**: Engine settings and optimization
- **Config/DefaultGame.ini**: Game-specific configuration
- **Config/DefaultInput.ini**: Input mappings and controls

### Build Configurations
Multiple project files support different deployment targets:
- **Development**: Full feature set with debugging
- **Clean**: Reduced feature set for testing
- **Minimal**: Bare-bones for performance validation
- **Shipping**: Optimized release build

## Development Environment

### Required Software
1. **Unreal Engine 5.6** (or 5.4+ compatibility)
2. **Visual Studio 2022** with C++ game development workload
3. **Git** with LFS extension for large file support
4. **Windows 11** (primary) or Windows 10 (minimum)

### Hardware Requirements
- **CPU**: Intel i7-8700K / AMD Ryzen 7 2700X (minimum)
- **GPU**: RTX 3060 / RX 6700XT (development target)
- **RAM**: 32GB (recommended), 16GB (minimum)
- **Storage**: 100GB+ SSD space for UE5 + project

### Development Scripts
Located in `/Scripts/` directory:
- `LaunchUE56_VS2022.bat`: Standard development launch
- `LaunchCleanUE56.bat`: Minimal feature testing
- `RunTests.bat`: Execute full TDD test suite
- `ValidateBuild.bat`: Pre-commit build validation
- `MCPIntegration.bat`: MCP tools integration

## Performance Architecture

### Frame Budget Allocation
Total: 8.33ms (120 FPS) / 16.67ms (60 FPS)

| System | Budget | Responsibility |
|--------|--------|----------------|
| Gameplay Logic | 2.0ms | Snake movement, power-ups, game state |
| Rendering | 3.5ms | 3D graphics, effects, post-processing |
| Audio | 0.5ms | Sound effects, music, spatial audio |
| Networking | 0.5ms | Multiplayer synchronization |
| Buffer | 1.8ms | Frame variance tolerance |

### Memory Management
- **Texture Streaming**: Dynamic loading based on camera distance
- **Object Pooling**: Reuse instances for projectiles, effects, enemies
- **Garbage Collection**: Optimized UE5 GC with manual cleanup hints
- **Platform Scaling**: Adaptive LOD and quality settings

### Platform Optimization
```cpp
// Adaptive settings based on hardware capability
FPlatformSettings {
    static bool ShouldUseLumen() {
        return FPlatformMisc::GetGPUTier() >= 3;
    }
    
    static int32 GetTargetFrameRate() {
        return IsHighEndGPU() ? 120 : 60;
    }
};
```

## Data Management

### Asset Organization
```
Content/
├── Audio/               # Music and sound effects by art style
├── Data/                # JSON data tables for game content
├── Materials/           # 5 art style material libraries
├── Meshes/              # 3D models and static meshes
├── Textures/            # Texture assets organized by style
├── UI/                  # User interface assets
├── VFX/                 # Visual effects and particle systems
└── Maps/                # Level and menu maps
```

### Data Tables (JSON)
- **DT_Powerups.json**: 48 unique power-up definitions
- **DT_Debuffs.json**: 32 debuff configurations  
- **DT_Enemies.json**: Enemy behavior and stats
- **DT_Cosmetics.json**: Unlockable visual content

### Version Control Strategy
- **Git LFS**: All binary assets (textures, audio, meshes)
- **Git Standard**: Source code, JSON data, configuration
- **Branching**: Feature branches with mandatory PR reviews
- **Hooks**: Pre-commit tests and code formatting validation

## Testing Infrastructure

### Test-Driven Development Framework
- **Unit Tests**: 95% coverage target for core logic
- **Integration Tests**: Cross-module communication validation
- **Performance Tests**: Frame budget compliance verification
- **Functional Tests**: End-to-end gameplay scenarios

### Automated Testing Pipeline
```yaml
# GitHub Actions workflow
Test Suite:
  - Compile all configurations
  - Execute unit test suite
  - Run integration tests
  - Validate performance benchmarks
  - Generate coverage reports
```

### Quality Assurance
- **Static Analysis**: PVS-Studio integration for C++ validation
- **Code Reviews**: Mandatory peer review for all changes
- **Continuous Integration**: Automated build and test on commit
- **Performance Monitoring**: Frame time tracking and regression detection

## Networking Architecture

### Multiplayer Design
- **Deterministic Simulation**: Fixed-point math for consistent results
- **Client-Server**: Authoritative server with client prediction
- **Rollback Netcode**: State snapshots for conflict resolution
- **Input Serialization**: Commands rather than state synchronization

### Platform Integration
- **Steam Networking**: Steam P2P for PC releases
- **Platform Services**: Achievement and leaderboard integration
- **Cross-Platform**: Potential future expansion to consoles
- **Room Codes**: 4-digit codes for easy friend connections

## Security & Compliance

### Data Protection
- **No Personal Data**: Game doesn't collect user information
- **Local Storage**: Save files stored locally only
- **Network Security**: Encrypted multiplayer communications
- **Content Rating**: Appropriate for all ages

### Build Security
- **Code Signing**: Signed executables for release builds
- **Asset Validation**: Integrity checks for game content
- **Update System**: Secure patch delivery mechanism
- **Anti-Cheat**: Basic validation for multiplayer integrity

## Deployment Pipeline

### Build Automation
1. **Source Validation**: Code formatting and static analysis
2. **Compilation**: Multi-platform build generation
3. **Testing**: Automated test suite execution
4. **Packaging**: Platform-specific distribution preparation
5. **Distribution**: Upload to itch.io and other platforms

### Release Management
- **Versioning**: Semantic versioning with build metadata
- **Staging**: Pre-production testing environment  
- **Rollback**: Ability to revert problematic releases
- **Monitoring**: Post-release performance and crash tracking

## Development Workflow

### Daily Development
1. **Pull latest changes** from main branch
2. **Run local tests** to ensure clean baseline
3. **Implement features** following TDD methodology
4. **Execute test suite** to validate changes
5. **Submit pull request** with automated validation

### Code Standards
- **Epic C++ Guidelines**: Naming conventions and structure
- **Documentation**: Comprehensive header comments
- **Performance Awareness**: Frame budget considerations
- **Memory Safety**: Smart pointers and RAII patterns

### Integration Points
- **MCP Tools**: 8 selected tools for enhanced development
- **VS Code Integration**: Augment Code extension support
- **Git Workflow**: Feature branches with protected main
- **CI/CD Pipeline**: GitHub Actions for automation
