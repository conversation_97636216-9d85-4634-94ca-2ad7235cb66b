// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/World.h"
#include "CameraRigActor.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogCameraRig, Log, All);

/**
 * Camera Impulse Types for different events
 */
UENUM(BlueprintType)
enum class ECameraImpulseType : uint8
{
    Collision       UMETA(DisplayName = "Collision"),
    Boost          UMETA(DisplayName = "Boost"),
    PowerupPickup  UMETA(DisplayName = "Powerup Pickup"),
    EnemyHit       UMETA(DisplayName = "Enemy Hit"),
    ArenaExpansion UMETA(DisplayName = "Arena Expansion")
};

/**
 * Camera Rig Actor
 * 
 * Dynamic tether-style camera system with spring-arm physics.
 * Handles impulse responses, FOV zoom, and smooth camera following.
 * 
 * Physics Formula: F = kx - dv
 * - k: Spring constant
 * - x: Displacement from target
 * - d: Damping coefficient  
 * - v: Velocity
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API ACameraRigActor : public AActor
{
    GENERATED_BODY()

public:
    /** Constructor */
    ACameraRigActor();

    /** AActor interface */
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    /**
     * Get the singleton instance of CameraRig
     * @param World The world context
     * @return Pointer to CameraRig instance, nullptr if not found
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    static ACameraRigActor* GetCameraRig(const UWorld* World);

    /**
     * Initialize the camera rig system
     * @return true if initialization successful, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    bool InitializeCameraRig();

    /**
     * Set the target actor to follow
     * @param NewTarget The actor to follow
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    void SetFollowTarget(AActor* NewTarget);

    /**
     * Apply impulse to the camera for shake effects
     * @param ImpulseType Type of impulse event
     * @param ImpulseMagnitude Strength of the impulse
     * @param ImpulseDirection Direction of the impulse (optional)
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    void ApplyCameraImpulse(ECameraImpulseType ImpulseType, float ImpulseMagnitude, FVector ImpulseDirection = FVector::ZeroVector);

    /**
     * Set FOV zoom multiplier (1.0 = normal, 1.1 = +10% zoom)
     * @param ZoomMultiplier The zoom multiplier to apply
     * @param bSmoothTransition Whether to smoothly transition to new FOV
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    void SetFOVZoom(float ZoomMultiplier, bool bSmoothTransition = true);

    /**
     * Get the camera component
     * @return Pointer to camera component
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    UCameraComponent* GetCameraComponent() const { return CameraComponent; }

    /**
     * Get the spring arm component
     * @return Pointer to spring arm component
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    USpringArmComponent* GetSpringArmComponent() const { return SpringArmComponent; }

    /**
     * Check if the camera rig is initialized
     * @return true if initialized, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Camera Rig")
    bool IsCameraRigInitialized() const { return bIsCameraRigInitialized; }

protected:
    /** Root scene component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig")
    USceneComponent* RootSceneComponent;

    /** Spring arm component for tether physics */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig")
    USpringArmComponent* SpringArmComponent;

    /** Camera component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig")
    UCameraComponent* CameraComponent;

    /** Target actor to follow */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig")
    AActor* FollowTarget;

    /** Spring constant for tether physics (k in F=kx-dv) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera Rig|Physics",
              meta = (ClampMin = "0.1", ClampMax = "10.0", ToolTip = "Spring force multiplier for camera tether"))
    float SpringConstant = 2.0f;

    /** Damping coefficient for tether physics (d in F=kx-dv) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera Rig|Physics",
              meta = (ClampMin = "0.1", ClampMax = "5.0", ToolTip = "Velocity damping for smooth camera movement"))
    float DampingCoefficient = 1.0f;

    /** Base FOV for the camera */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera Rig|FOV", meta = (ClampMin = "30.0", ClampMax = "120.0"))
    float BaseFOV = 90.0f;

    /** Current FOV zoom multiplier */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig|FOV")
    float CurrentFOVZoom = 1.0f;

    /** Target FOV zoom multiplier */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig|FOV")
    float TargetFOVZoom = 1.0f;

    /** FOV transition speed */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Camera Rig|FOV", meta = (ClampMin = "0.1", ClampMax = "10.0"))
    float FOVTransitionSpeed = 3.0f;

    /** Flag indicating if camera rig is initialized */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig")
    bool bIsCameraRigInitialized = false;

    /** Flag indicating if this is the singleton instance */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Camera Rig")
    bool bIsSingletonInstance = false;

private:
    /** Static reference to singleton instance */
    static ACameraRigActor* SingletonInstance;

    /** Current camera velocity for physics calculations */
    FVector CameraVelocity = FVector::ZeroVector;

    /** Previous camera position for velocity calculation */
    FVector PreviousCameraPosition = FVector::ZeroVector;

    /** Current impulse force being applied */
    FVector CurrentImpulseForce = FVector::ZeroVector;

    /** Update spring-arm tether physics */
    void UpdateSpringArmTether(float DeltaTime);

    /** Update FOV zoom transitions */
    void UpdateFOVZoom(float DeltaTime);

    /** Update impulse physics */
    void UpdateImpulsePhysics(float DeltaTime);

    /** Calculate spring force based on displacement */
    FVector CalculateSpringForce(const FVector& Displacement) const;

    /** Calculate damping force based on velocity */
    FVector CalculateDampingForce(const FVector& Velocity) const;

    /** Validate singleton instance */
    void ValidateSingletonInstance();
};
