// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameStateBase.h"
#include "Engine/World.h"
#include "CoreLoopGameState.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogCoreLoop, Log, All);

/**
 * Game Flow States for Snake Rogue
 */
UENUM(BlueprintType)
enum class EGameFlowState : uint8
{
    Menu        UMETA(DisplayName = "Menu"),
    Playing     UMETA(DisplayName = "Playing"),
    Paused      UMETA(DisplayName = "Paused"),
    GameOver    UMETA(DisplayName = "Game Over"),
    Loading     UMETA(DisplayName = "Loading")
};

/**
 * CoreLoop Game State
 * 
 * Singleton UGameState that manages the core game loop and flow.
 * Handles communication between all major game modules.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API ACoreLoopGameState : public AGameStateBase
{
    GENERATED_BODY()

public:
    /** Constructor */
    ACoreLoopGameState();

    /** AGameStateBase interface */
    virtual void BeginPlay() override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;
    virtual void Tick(float DeltaTime) override;

    /**
     * Get the singleton instance of CoreLoop GameState
     * @param World The world context
     * @return Pointer to CoreLoop GameState instance, nullptr if not found
     */
    UFUNCTION(BlueprintCallable, Category = "Core Loop")
    static ACoreLoopGameState* GetCoreLoopGameState(const UWorld* World);

    /**
     * Initialize the core game systems
     * @return true if initialization successful, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Core Loop")
    bool InitializeCoreGameSystems();

    /**
     * Shutdown the core game systems
     */
    UFUNCTION(BlueprintCallable, Category = "Core Loop")
    void ShutdownCoreGameSystems();

    /**
     * Get the current game flow state
     * @return Current game flow state
     */
    UFUNCTION(BlueprintCallable, Category = "Core Loop")
    EGameFlowState GetGameFlowState() const { return CurrentGameFlowState; }

    /**
     * Set the game flow state
     * @param NewState The new game flow state to set
     */
    UFUNCTION(BlueprintCallable, Category = "Core Loop")
    void SetGameFlowState(EGameFlowState NewState);

    /**
     * Check if the core systems are initialized
     * @return true if initialized, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Core Loop")
    bool AreCoreSystemsInitialized() const { return bAreCoreSystemsInitialized; }

protected:
    /** Current game flow state */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Core Loop")
    EGameFlowState CurrentGameFlowState = EGameFlowState::Menu;

    /** Flag indicating if core systems are initialized */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Core Loop")
    bool bAreCoreSystemsInitialized = false;

    /** Flag indicating if this is the singleton instance */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Core Loop")
    bool bIsSingletonInstance = false;

private:
    /** Static reference to singleton instance */
    static ACoreLoopGameState* SingletonInstance;

    /** Initialize module communication */
    void InitializeModuleCommunication();

    /** Shutdown module communication */
    void ShutdownModuleCommunication();

    /** Handle game flow state transitions */
    void HandleGameFlowStateTransition(EGameFlowState OldState, EGameFlowState NewState);

    /** Validate singleton instance */
    void ValidateSingletonInstance();
};
