// Copyright Epic Games, Inc. All Rights Reserved.

#include "SnakeRogueGameMode.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "CoreLoopGameState.h"
#include "CameraRigActor.h"
#include "LevelGenActor.h"
#include "PowerupSystemActor.h"
#include "EnemyManager/EnemyManagerActor.h"
#include "NetSync/NetSyncActor.h"

DEFINE_LOG_CATEGORY(LogSnakeRogueGameMode);

ASnakeRogueGameMode::ASnakeRogueGameMode()
{
    // Set this actor to call Tick() every frame
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;

    // Set default game state class
    GameStateClass = ACoreLoopGameState::StaticClass();

    // Initialize default values
    bAllSystemsInitialized = false;
    bGameActive = false;

    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("SnakeRogue GameMode constructor called"));
}

void ASnakeRogueGameMode::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("SnakeRogue GameMode BeginPlay started"));

    // Initialize all game systems
    if (InitializeGameSystems())
    {
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("All game systems initialized successfully"));
        bAllSystemsInitialized = true;
    }
    else
    {
        UE_LOG(LogSnakeRogueGameMode, Error, TEXT("Failed to initialize game systems"));
    }

    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("SnakeRogue GameMode BeginPlay completed"));
}

void ASnakeRogueGameMode::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("SnakeRogue GameMode EndPlay started"));

    // End any active game
    if (bGameActive)
    {
        EndGame();
    }

    bAllSystemsInitialized = false;

    Super::EndPlay(EndPlayReason);

    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("SnakeRogue GameMode EndPlay completed"));
}

void ASnakeRogueGameMode::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Monitor system health
    if (bAllSystemsInitialized && !ValidateAllSystems())
    {
        UE_LOG(LogSnakeRogueGameMode, Warning, TEXT("System validation failed during runtime"));
    }
}

bool ASnakeRogueGameMode::InitializeGameSystems()
{
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Initializing game systems..."));

    // Initialize core modules
    if (!InitializeCoreModules())
    {
        UE_LOG(LogSnakeRogueGameMode, Error, TEXT("Failed to initialize core modules"));
        return false;
    }

    // Setup module communication
    SetupModuleCommunication();

    // Validate all systems
    if (!ValidateAllSystems())
    {
        UE_LOG(LogSnakeRogueGameMode, Error, TEXT("System validation failed"));
        return false;
    }

    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Game systems initialized successfully"));
    return true;
}

void ASnakeRogueGameMode::StartNewGame()
{
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Starting new game..."));

    if (!bAllSystemsInitialized)
    {
        UE_LOG(LogSnakeRogueGameMode, Error, TEXT("Cannot start game - systems not initialized"));
        return;
    }

    // Set game state to playing
    if (ACoreLoopGameState* CoreLoop = ACoreLoopGameState::GetCoreLoopGameState(GetWorld()))
    {
        CoreLoop->SetGameFlowState(EGameFlowState::Playing);
    }

    bGameActive = true;
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("New game started"));
}

void ASnakeRogueGameMode::EndGame()
{
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Ending game..."));

    // Set game state to game over
    if (ACoreLoopGameState* CoreLoop = ACoreLoopGameState::GetCoreLoopGameState(GetWorld()))
    {
        CoreLoop->SetGameFlowState(EGameFlowState::GameOver);
    }

    bGameActive = false;
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Game ended"));
}

bool ASnakeRogueGameMode::InitializeCoreModules()
{
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Initializing core modules..."));

    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogSnakeRogueGameMode, Error, TEXT("No world context available"));
        return false;
    }

    // Spawn core modules if they don't exist
    if (!ACoreLoopGameState::GetCoreLoopGameState(World))
    {
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("CoreLoop will be created by GameState"));
    }

    if (!ACameraRigActor::GetCameraRig(World))
    {
        World->SpawnActor<ACameraRigActor>();
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Spawned CameraRig"));
    }

    if (!ALevelGenActor::GetLevelGen(World))
    {
        World->SpawnActor<ALevelGenActor>();
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Spawned LevelGen"));
    }

    if (!APowerupSystemActor::GetPowerupSystem(World))
    {
        World->SpawnActor<APowerupSystemActor>();
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Spawned PowerupSystem"));
    }

    if (!AEnemyManagerActor::GetEnemyManager(World))
    {
        World->SpawnActor<AEnemyManagerActor>();
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Spawned EnemyManager"));
    }

    if (!ANetSyncActor::GetNetSync(World))
    {
        World->SpawnActor<ANetSyncActor>();
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Spawned NetSync"));
    }

    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Core modules initialized"));
    return true;
}

void ASnakeRogueGameMode::SetupModuleCommunication()
{
    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Setting up module communication..."));

    UWorld* World = GetWorld();
    if (!World)
    {
        return;
    }

    // Get all modules
    ACoreLoopGameState* CoreLoop = ACoreLoopGameState::GetCoreLoopGameState(World);
    ACameraRigActor* CameraRig = ACameraRigActor::GetCameraRig(World);
    ALevelGenActor* LevelGen = ALevelGenActor::GetLevelGen(World);

    // Setup camera to follow arena center
    if (CameraRig && LevelGen)
    {
        // Set initial camera position based on arena
        FVector ArenaCenter = FVector::ZeroVector;
        CameraRig->SetFollowTarget(nullptr); // Will follow arena center
        UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Camera setup complete"));
    }

    UE_LOG(LogSnakeRogueGameMode, Log, TEXT("Module communication setup complete"));
}

bool ASnakeRogueGameMode::ValidateAllSystems()
{
    UWorld* World = GetWorld();
    if (!World)
    {
        return false;
    }

    // Validate all core modules exist and are initialized
    ACoreLoopGameState* CoreLoop = ACoreLoopGameState::GetCoreLoopGameState(World);
    ACameraRigActor* CameraRig = ACameraRigActor::GetCameraRig(World);
    ALevelGenActor* LevelGen = ALevelGenActor::GetLevelGen(World);
    APowerupSystemActor* PowerupSystem = APowerupSystemActor::GetPowerupSystem(World);
    AEnemyManagerActor* EnemyManager = AEnemyManagerActor::GetEnemyManager(World);
    ANetSyncActor* NetSync = ANetSyncActor::GetNetSync(World);

    bool bAllValid = true;
    bAllValid &= (CoreLoop && CoreLoop->AreCoreSystemsInitialized());
    bAllValid &= (CameraRig && CameraRig->IsCameraRigInitialized());
    bAllValid &= (LevelGen && LevelGen->IsLevelGenInitialized());
    bAllValid &= (PowerupSystem && PowerupSystem->IsPowerupSystemInitialized());
    bAllValid &= (EnemyManager && EnemyManager->IsEnemyManagerInitialized());
    bAllValid &= (NetSync && NetSync->IsNetSyncInitialized());

    return bAllValid;
}
