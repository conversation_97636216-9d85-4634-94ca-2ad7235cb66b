@echo off
echo ========================================
echo    SNAKE ROGUE - UE 5.6 + MCP LAUNCH
echo ========================================
echo.

echo [INFO] Launching Snake Rogue with Unreal Engine 5.6 and MCP Server...
echo.

REM Check if UE 5.6 is available
echo [CHECK] Looking for Unreal Engine 5.6...
set UE_PATH=""

if exist "C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe" (
    set UE_PATH="C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"
    echo ✅ Found UE 5.6 at Epic Games location
) else if exist "C:\Program Files\Unreal Engine\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe" (
    set UE_PATH="C:\Program Files\Unreal Engine\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe"
    echo ✅ Found UE 5.6 at Unreal Engine location
) else (
    echo ❌ UE 5.6 not found at standard locations
    echo [INFO] Trying to launch project file directly...
    goto :direct_launch
)

echo.
echo [INFO] Launching with full UE 5.6 + MCP integration...
echo.

REM Launch with MCP-friendly parameters
%UE_PATH% ^
    "SnakeRogue.uproject" ^
    -log ^
    -stdout ^
    -AllowStdOutLogVerbosity ^
    -EnableMCPServer ^
    -MCPPort=3000

goto :end

:direct_launch
echo.
echo [INFO] Launching project file directly...
echo [INFO] This will auto-detect your UE 5.6 installation
echo.

start "" "SnakeRogue.uproject"

echo ✅ Project launched!
echo.
echo Expected behavior:
echo   1. UE 5.6 opens automatically
echo   2. Project files generate (2-5 minutes)
echo   3. C++ modules compile (5-15 minutes)
echo   4. Snake Rogue project opens successfully!
echo.

:end
echo.
echo ========================================
echo    LAUNCH SEQUENCE COMPLETE!
echo ========================================
echo.
echo 🎮 SNAKE ROGUE + UE 5.6 + MCP = ULTIMATE POWER! 🎮
echo.
echo Next steps:
echo   1. ✅ Wait for project to open in UE 5.6
echo   2. ✅ Run automation tests (Window → Developer Tools)
echo   3. ✅ Explore our 6 core modules
echo   4. ✅ Test the MCP server integration
echo.
pause
