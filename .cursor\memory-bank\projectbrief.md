# Snake Rogue - Project Brief

## Project Identity
**Name:** Snake Rogue  
**Version:** 0.1  
**Date:** June 14, 2025  
**Status:** Development Phase - 98% Architecture Complete

## Core Vision
Snake Rogue is a fully 3D, isometric, procedurally expanding roguelike that fuses the timeless "grow-your-tail" tension of <PERSON> with the high-energy chaos of bullet-hell and dungeon-crawlers. Built in Unreal Engine 5.6, targeting buttery-smooth 120 FPS at 4K on RTX 3060 while gracefully scaling down to integrated GPUs.

## Game Pillars
1. **Momentum** - You are always growing, the arena is always expanding; motion never stops
2. **Chaos & Mastery** - Dozens of synergistic power-ups and punishing debuffs create delightful mayhem but reward practiced control  
3. **Replay Forever** - Meta-progression perks + cosmetics ensure "just one more run"

## Technical Foundation
- **Engine:** Unreal Engine 5.6 (targeting 5.4 compatibility)
- **Language:** C++ with Blueprint integration (90% C++ for determinism)
- **Development Method:** Test-Driven Development (TDD) with Red-Green-Refactor cycles
- **Architecture:** Modular C++ design following Epic coding standards
- **Testing:** 95% line coverage for core logic, 100% for critical paths

## Performance Targets
| Tier | Platform | GPU | Target FPS |
|------|----------|-----|------------|
| Dev & Release A | Windows 11, Arch Linux | RTX 3060 / RX 6700XT | 120 FPS @ 4K |
| Release B | SteamDeck / mobile-class PCs | RDNA2 APU | 60 FPS @ 1080p |
| Stretch | Xbox Series S/X, PS5 | Console GDK | 60 FPS @ 4K |

## Core Requirements
- Procedural arena expansion every 4 seconds
- 48 unique power-ups across 8 families
- 32 debuffs with novel effects
- 5 unlockable art styles (Neon Synthwave, Pastel Candy, etc.)
- Co-op and PvP multiplayer with deterministic networking
- Frame budget: 8.3ms total (6.5ms gameplay + 1.8ms buffer)

## Success Criteria
- Smooth 120 FPS performance on target hardware
- Complete TDD test coverage with automated CI/CD
- Modular architecture supporting easy feature addition
- Multi-platform deployment readiness
- Professional-grade code quality following Epic standards

## Constraints
- Free itch.io release (no monetization)
- Must maintain deterministic gameplay for multiplayer
- Lumen enabled for high-tier, Nanite disabled
- Git + Git LFS for version control with binary asset locks
