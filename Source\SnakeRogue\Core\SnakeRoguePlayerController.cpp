// Copyright Epic Games, Inc. All Rights Reserved.

#include "SnakeRoguePlayerController.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "Components/InputComponent.h"
#include "CameraRigActor.h"
#include "PowerupSystemActor.h"
#include "CoreLoopGameState.h"

DEFINE_LOG_CATEGORY(LogSnakeRoguePlayerController);

ASnakeRoguePlayerController::ASnakeRoguePlayerController()
{
    // Set this controller to call Tick() every frame
    PrimaryActorTick.bCanEverTick = true;
    PrimaryActorTick.bStartWithTickEnabled = true;

    // Initialize default values
    MovementInput = FVector2D::ZeroVector;
    bSlitherSurgeActive = false;
    bScoreboardShown = false;

    UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("SnakeRogue PlayerController constructor called"));
}

void ASnakeRoguePlayerController::BeginPlay()
{
    Super::BeginPlay();

    UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("SnakeRogue PlayerController BeginPlay started"));

    // Setup camera
    if (ACameraRigActor* CameraRig = ACameraRigActor::GetCameraRig(GetWorld()))
    {
        SetViewTarget(CameraRig);
        UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("Camera view target set"));
    }

    UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("SnakeRogue PlayerController BeginPlay completed"));
}

void ASnakeRoguePlayerController::SetupInputComponent()
{
    Super::SetupInputComponent();

    if (InputComponent)
    {
        // Movement bindings
        InputComponent->BindAction("MoveUp", IE_Pressed, this, &ASnakeRoguePlayerController::HandleMoveUp);
        InputComponent->BindAction("MoveDown", IE_Pressed, this, &ASnakeRoguePlayerController::HandleMoveDown);
        InputComponent->BindAction("MoveLeft", IE_Pressed, this, &ASnakeRoguePlayerController::HandleMoveLeft);
        InputComponent->BindAction("MoveRight", IE_Pressed, this, &ASnakeRoguePlayerController::HandleMoveRight);

        // Action bindings
        InputComponent->BindAction("ActivatePowerup", IE_Pressed, this, &ASnakeRoguePlayerController::HandleActivatePowerup);
        InputComponent->BindAction("SlitherSurge", IE_Pressed, this, &ASnakeRoguePlayerController::HandleSlitherSurge);
        InputComponent->BindAction("Pause", IE_Pressed, this, &ASnakeRoguePlayerController::HandlePause);
        InputComponent->BindAction("ShowScoreboard", IE_Pressed, this, &ASnakeRoguePlayerController::HandleShowScoreboard);

        // Axis bindings
        InputComponent->BindAxis("MoveHorizontal", this, &ASnakeRoguePlayerController::HandleMoveLeft);
        InputComponent->BindAxis("MoveVertical", this, &ASnakeRoguePlayerController::HandleMoveUp);

        UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("Input component setup complete"));
    }
}

void ASnakeRoguePlayerController::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

    // Process movement input
    ProcessMovementInput(DeltaTime);

    // Update camera input
    UpdateCameraInput(DeltaTime);
}

void ASnakeRoguePlayerController::HandleMoveUp(float Value)
{
    MovementInput.Y = FMath::Clamp(Value, -1.0f, 1.0f);
    UE_LOG(LogSnakeRoguePlayerController, VeryVerbose, TEXT("Move Up: %.2f"), Value);
}

void ASnakeRoguePlayerController::HandleMoveDown(float Value)
{
    MovementInput.Y = FMath::Clamp(-Value, -1.0f, 1.0f);
    UE_LOG(LogSnakeRoguePlayerController, VeryVerbose, TEXT("Move Down: %.2f"), Value);
}

void ASnakeRoguePlayerController::HandleMoveLeft(float Value)
{
    MovementInput.X = FMath::Clamp(-Value, -1.0f, 1.0f);
    UE_LOG(LogSnakeRoguePlayerController, VeryVerbose, TEXT("Move Left: %.2f"), Value);
}

void ASnakeRoguePlayerController::HandleMoveRight(float Value)
{
    MovementInput.X = FMath::Clamp(Value, -1.0f, 1.0f);
    UE_LOG(LogSnakeRoguePlayerController, VeryVerbose, TEXT("Move Right: %.2f"), Value);
}

void ASnakeRoguePlayerController::HandleActivatePowerup()
{
    UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("Activate Powerup pressed"));

    // TODO: Implement powerup activation logic
    if (APowerupSystemActor* PowerupSystem = APowerupSystemActor::GetPowerupSystem(GetWorld()))
    {
        // Apply a test powerup
        PowerupSystem->ApplyPowerup(TEXT("Powerup_0"), GetPawn());
    }
}

void ASnakeRoguePlayerController::HandleSlitherSurge()
{
    bSlitherSurgeActive = !bSlitherSurgeActive;
    UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("Slither Surge: %s"), bSlitherSurgeActive ? TEXT("ON") : TEXT("OFF"));

    // Apply camera FOV zoom during surge
    if (ACameraRigActor* CameraRig = ACameraRigActor::GetCameraRig(GetWorld()))
    {
        float ZoomMultiplier = bSlitherSurgeActive ? 1.1f : 1.0f;
        CameraRig->SetFOVZoom(ZoomMultiplier, true);
    }
}

void ASnakeRoguePlayerController::HandlePause()
{
    UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("Pause pressed"));

    if (ACoreLoopGameState* CoreLoop = ACoreLoopGameState::GetCoreLoopGameState(GetWorld()))
    {
        EGameFlowState CurrentState = CoreLoop->GetGameFlowState();
        if (CurrentState == EGameFlowState::Playing)
        {
            CoreLoop->SetGameFlowState(EGameFlowState::Paused);
        }
        else if (CurrentState == EGameFlowState::Paused)
        {
            CoreLoop->SetGameFlowState(EGameFlowState::Playing);
        }
    }
}

void ASnakeRoguePlayerController::HandleShowScoreboard()
{
    bScoreboardShown = !bScoreboardShown;
    UE_LOG(LogSnakeRoguePlayerController, Log, TEXT("Scoreboard: %s"), bScoreboardShown ? TEXT("SHOWN") : TEXT("HIDDEN"));

    // TODO: Implement scoreboard UI toggle
}

void ASnakeRoguePlayerController::ProcessMovementInput(float DeltaTime)
{
    if (MovementInput.IsNearlyZero())
    {
        return;
    }

    // TODO: Send movement input to snake actor
    // For now, just log the input
    UE_LOG(LogSnakeRoguePlayerController, VeryVerbose, TEXT("Movement Input: X=%.2f, Y=%.2f"), 
           MovementInput.X, MovementInput.Y);
}

void ASnakeRoguePlayerController::UpdateCameraInput(float DeltaTime)
{
    // TODO: Handle camera zoom input
    // Camera follows automatically via CameraRig system
}
