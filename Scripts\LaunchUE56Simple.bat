@echo off
echo ========================================
echo    SNAKE ROGUE - UE5.6 SIMPLE LAUNCH
echo ========================================
echo.

echo [INFO] Launching UE5.6 without project file generation...
echo [INFO] UE5.6 will generate project files automatically
echo.

REM Clean up problematic files
echo [CLEANUP] Removing problematic cache files...
if exist "Binaries" rmdir /s /q "Binaries" 2>nul
if exist "Intermediate" rmdir /s /q "Intermediate" 2>nul
echo   ✅ Cache cleared!
echo.

REM Find and launch UE5.6 directly
echo [LAUNCH] Finding UE5.6 installation...

set UE_FOUND=0
for /d %%i in ("C:\Program Files\Epic Games\UE_5.*") do (
    if exist "%%i\Engine\Binaries\Win64\UnrealEditor.exe" (
        echo   Found UE at: %%i
        echo   Launching directly...
        echo.
        start "" "%%i\Engine\Binaries\Win64\UnrealEditor.exe" "%CD%\SnakeRogue.uproject"
        set UE_FOUND=1
        goto :launched
    )
)

:launched
if %UE_FOUND%==1 (
    echo ✅ UE5.6 launched!
    echo.
    echo IMPORTANT NOTES:
    echo   1. UE5.6 will ask to convert/upgrade the project - CLICK YES
    echo   2. If it asks about missing modules - CLICK YES to rebuild
    echo   3. If compilation fails, try: Build → Compile SnakeRogue
    echo   4. Some advanced features may need VS2022 for full compilation
    echo.
    echo The project should open and basic functionality will work!
) else (
    echo ❌ UE5.6 not found!
    echo Please make sure Unreal Engine 5.6 is installed.
)

echo.
pause
