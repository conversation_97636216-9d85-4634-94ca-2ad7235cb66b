# User Stories & Flows – **Snake Rogue** (Beginner‑Friendly)

## How to read this file
*A **user story** describes a feature from the player’s viewpoint. A **flow** shows the step‑by‑step path.*

### Common Terms
* **Arena** – the play field.
* **Buff** – a good temporary effect.
* **Debuff** – a bad temporary effect.
* **Meta‑currency** – points you spend between runs.

---

## Stories

| ID | Story | Acceptance Criteria |
|----|-------|---------------------|
| S‑01 | *As a new player, I want a short tutorial so I’m not lost.* | Tutorial can be finished in <3 min; player learns turn, collect, avoid walls. |
| S‑02 | *As a player, I want the arena to keep expanding so I feel constant pressure.* | No soft caps; arena grows every 4 s. |
| S‑03 | *As a power user, I want to trigger Slow‑Motion so I can dodge tricky patterns.* | “Q” key triggers buff if in inventory; cooldown shown. |
| S‑04 | *As a collector, I want cosmetic skins to show off my progress.* | Unlock screen shows % collected; equip from menu. |

## Flows

### First‑Time Flow
1. Launch game.
2. Title screen → *Press Start*.
3. Tutorial arena loads.
4. Complete basic tasks → pop‑up “Ready!” → enters Run #1.

### Regular Run Flow
```text
Menu → Select Mode → Countdown → Play (score, power‑ups) → Death → Score Screen → Upgrade & Cosmetic Shop → Menu
```

### Multiplayer Host Flow
1. Menu → Multiplayer → **Host Game**.
2. Share 4‑digit code.
3. Friends join → Ready check → Start.
