// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogSnakeRogue, Log, All);

/**
 * Snake Rogue Game Module
 * 
 * Main module for the Snake Rogue game. Handles module initialization,
 * logging setup, and core game systems registration.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
class FSnakeRogueModule : public IModuleInterface
{
public:
	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

	/**
	 * Check if the module is loaded and ready
	 * @return true if module is loaded, false otherwise
	 */
	static bool IsModuleLoaded();

	/**
	 * Get the Snake Rogue module instance
	 * @return Reference to the module instance
	 */
	static FSnakeRogueModule& Get();

private:
	/** Flag indicating if module has been initialized */
	bool bIsInitialized = false;

	/** Initialize core game systems */
	void InitializeCoreGameSystems();

	/** Shutdown core game systems */
	void ShutdownCoreGameSystems();

	/** Register automation tests for TDD workflow */
	void RegisterAutomationTests();

	/** Unregister automation tests */
	void UnregisterAutomationTests();
};
