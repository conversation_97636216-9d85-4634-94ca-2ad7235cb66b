// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/World.h"
#include "LevelGenActor.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogLevelGen, Log, All);

/**
 * Tile Types for the arena
 */
UENUM(BlueprintType)
enum class ETileType : uint8
{
    Floor       UMETA(DisplayName = "Floor"),
    Wall        UMETA(DisplayName = "Wall"),
    Special     UMETA(DisplayName = "Special"),
    Fog         UMETA(DisplayName = "Fog")
};

/**
 * Growth Direction for arena expansion
 */
UENUM(BlueprintType)
enum class EGrowthDirection : uint8
{
    North       UMETA(DisplayName = "North"),
    South       UMETA(DisplayName = "South"),
    East        UMETA(DisplayName = "East"),
    West        UMETA(DisplayName = "West"),
    NorthEast   UMETA(DisplayName = "North East"),
    NorthWest   UMETA(DisplayName = "North West"),
    SouthEast   UMETA(DisplayName = "South East"),
    SouthWest   UMETA(DisplayName = "South West")
};

/**
 * Tile Data Structure
 */
USTRUCT(BlueprintType)
struct FTileData
{
    GENERATED_BODY()

    /** Grid position of the tile */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
    FIntPoint GridPosition = FIntPoint::ZeroValue;

    /** World position of the tile */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
    FVector WorldPosition = FVector::ZeroVector;

    /** Type of the tile */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
    ETileType TileType = ETileType::Floor;

    /** Static mesh component for the tile */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Tile")
    UStaticMeshComponent* MeshComponent = nullptr;

    /** Whether this tile is active */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Tile")
    bool bIsActive = true;

    FTileData()
    {
        GridPosition = FIntPoint::ZeroValue;
        WorldPosition = FVector::ZeroVector;
        TileType = ETileType::Floor;
        MeshComponent = nullptr;
        bIsActive = true;
    }
};

/**
 * Level Generation Actor
 * 
 * Handles endless expanding arena generation with procedural tile spawning.
 * Manages arena expansion every 4 seconds, growth direction calculation,
 * and fog boundary system for performance optimization.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API ALevelGenActor : public AActor
{
    GENERATED_BODY()

public:
    /** Constructor */
    ALevelGenActor();

    /** AActor interface */
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    /**
     * Get the singleton instance of LevelGen
     * @param World The world context
     * @return Pointer to LevelGen instance, nullptr if not found
     */
    UFUNCTION(BlueprintCallable, Category = "Level Gen")
    static ALevelGenActor* GetLevelGen(const UWorld* World);

    /**
     * Initialize the level generation system
     * @return true if initialization successful, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Level Gen")
    bool InitializeLevelGen();

    /**
     * Set the snake centroid position for growth direction calculation
     * @param SnakeCentroid The current centroid position of the snake
     */
    UFUNCTION(BlueprintCallable, Category = "Level Gen")
    void SetSnakeCentroid(const FVector& SnakeCentroid);

    /**
     * Force arena expansion (for testing or special events)
     */
    UFUNCTION(BlueprintCallable, Category = "Level Gen")
    void ForceArenaExpansion();

    /**
     * Get the current arena size
     * @return Current arena size in grid units
     */
    UFUNCTION(BlueprintCallable, Category = "Level Gen")
    FIntPoint GetArenaSize() const { return CurrentArenaSize; }

    /**
     * Get tile at specific grid position
     * @param GridPosition The grid position to query
     * @param OutTileData The tile data output (if found)
     * @return true if tile found, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Level Gen")
    bool GetTileAtPosition(const FIntPoint& GridPosition, FTileData& OutTileData);

    /**
     * Check if the level generation system is initialized
     * @return true if initialized, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Level Gen")
    bool IsLevelGenInitialized() const { return bIsLevelGenInitialized; }

protected:
    /** Root scene component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Level Gen")
    USceneComponent* RootSceneComponent;

    /** Tile size in world units */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Gen|Settings", meta = (ClampMin = "50.0", ClampMax = "500.0"))
    float TileSize = 100.0f;

    /** Arena expansion interval in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Gen|Settings", meta = (ClampMin = "1.0", ClampMax = "10.0"))
    float ExpansionInterval = 4.0f;

    /** Initial arena size */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Gen|Settings")
    FIntPoint InitialArenaSize = FIntPoint(10, 10);

    /** Maximum arena size for performance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Gen|Settings")
    FIntPoint MaxArenaSize = FIntPoint(100, 100);

    /** Fog boundary distance from arena edge */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Level Gen|Settings", meta = (ClampMin = "1", ClampMax = "20"))
    int32 FogBoundaryDistance = 5;

    /** Current arena size */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Level Gen")
    FIntPoint CurrentArenaSize = FIntPoint::ZeroValue;

    /** Current snake centroid position */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Level Gen")
    FVector SnakeCentroidPosition = FVector::ZeroVector;

    /** Timer for arena expansion */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Level Gen")
    float ExpansionTimer = 0.0f;

    /** Flag indicating if level gen is initialized */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Level Gen")
    bool bIsLevelGenInitialized = false;

    /** Flag indicating if this is the singleton instance */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Level Gen")
    bool bIsSingletonInstance = false;

    /** Map of all active tiles */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Level Gen")
    TMap<FIntPoint, FTileData> ActiveTiles;

private:
    /** Static reference to singleton instance */
    static ALevelGenActor* SingletonInstance;

    /** Pool of reusable tile mesh components */
    TArray<UStaticMeshComponent*> TilePool;

    /** Initialize the initial arena */
    void InitializeArena();

    /** Update arena expansion timer */
    void UpdateExpansionTimer(float DeltaTime);

    /** Perform arena expansion */
    void PerformArenaExpansion();

    /** Calculate growth direction based on snake centroid */
    EGrowthDirection CalculateGrowthDirection() const;

    /** Spawn tiles in the specified direction */
    void SpawnTilesInDirection(EGrowthDirection Direction, int32 TileCount = 1);

    /** Create a new tile at grid position */
    FTileData CreateTile(const FIntPoint& GridPosition, ETileType TileType);

    /** Get or create tile mesh component from pool */
    UStaticMeshComponent* GetTileMeshFromPool();

    /** Return tile mesh component to pool */
    void ReturnTileMeshToPool(UStaticMeshComponent* MeshComponent);

    /** Update fog boundary and despawn distant tiles */
    void UpdateFogBoundary();

    /** Convert grid position to world position */
    FVector GridToWorldPosition(const FIntPoint& GridPosition) const;

    /** Convert world position to grid position */
    FIntPoint WorldToGridPosition(const FVector& WorldPosition) const;

    /** Validate singleton instance */
    void ValidateSingletonInstance();
};
