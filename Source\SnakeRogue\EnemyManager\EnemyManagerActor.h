// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/SceneComponent.h"
#include "EnemyManagerActor.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogEnemyManager, Log, All);

/**
 * Enemy Types
 */
UENUM(BlueprintType)
enum class EEnemyType : uint8
{
    WallTurret      UMETA(DisplayName = "Wall Turret"),
    Chaser          UMETA(DisplayName = "Chaser"),
    StaticMine      UMETA(DisplayName = "Static Mine"),
    MovingSawblade  UMETA(DisplayName = "Moving Sawblade")
};

/**
 * Enemy Manager Actor
 * 
 * Handles hazard spawning and AI for all enemy types.
 * Manages Wall Turrets, Chasers, Static Mines, and Moving Sawblades.
 * 
 * Following Epic C++ Coding Standards:
 * - 4-space indentation
 * - PascalCase for classes/structs
 * - bPrefixed booleans
 * - VerbNoun() function naming
 */
UCLASS(BlueprintType, Blueprintable)
class SNAKEROGUE_API AEnemyManagerActor : public AActor
{
    GENERATED_BODY()

public:
    /** Constructor */
    AEnemyManagerActor();

    /** AActor interface */
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

    /**
     * Get the singleton instance of EnemyManager
     * @param World The world context
     * @return Pointer to EnemyManager instance, nullptr if not found
     */
    UFUNCTION(BlueprintCallable, Category = "Enemy Manager")
    static AEnemyManagerActor* GetEnemyManager(const UWorld* World);

    /**
     * Initialize the enemy management system
     * @return true if initialization successful, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Enemy Manager")
    bool InitializeEnemyManager();

    /**
     * Spawn an enemy of the specified type
     * @param EnemyType The type of enemy to spawn
     * @param SpawnLocation The location to spawn the enemy
     * @return Pointer to spawned enemy actor, nullptr if failed
     */
    UFUNCTION(BlueprintCallable, Category = "Enemy Manager")
    AActor* SpawnEnemy(EEnemyType EnemyType, const FVector& SpawnLocation);

    /**
     * Spawn wall turrets around arena perimeter
     * @param TurretCount Number of turrets to spawn
     */
    UFUNCTION(BlueprintCallable, Category = "Enemy Manager")
    void SpawnWallTurrets(int32 TurretCount = 4);

    /**
     * Spawn chaser enemies
     * @param ChaserCount Number of chasers to spawn
     */
    UFUNCTION(BlueprintCallable, Category = "Enemy Manager")
    void SpawnChasers(int32 ChaserCount = 2);

    /**
     * Check if enemy manager is initialized
     * @return true if initialized, false otherwise
     */
    UFUNCTION(BlueprintCallable, Category = "Enemy Manager")
    bool IsEnemyManagerInitialized() const { return bIsEnemyManagerInitialized; }

    /**
     * Get all active enemies
     * @return Array of active enemy actors
     */
    UFUNCTION(BlueprintCallable, Category = "Enemy Manager")
    TArray<AActor*> GetActiveEnemies() const { return ActiveEnemies; }

protected:
    /** Root scene component */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Enemy Manager")
    USceneComponent* RootSceneComponent;

    /** Enemy spawn interval in seconds */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemy Manager|Settings", meta = (ClampMin = "1.0", ClampMax = "30.0"))
    float EnemySpawnInterval = 10.0f;

    /** Maximum number of active enemies */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Enemy Manager|Settings", meta = (ClampMin = "1", ClampMax = "50"))
    int32 MaxActiveEnemies = 20;

    /** Flag indicating if enemy manager is initialized */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Enemy Manager")
    bool bIsEnemyManagerInitialized = false;

    /** Flag indicating if this is the singleton instance */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Enemy Manager")
    bool bIsSingletonInstance = false;

    /** Array of active enemies */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Enemy Manager")
    TArray<AActor*> ActiveEnemies;

    /** Timer for enemy spawning */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Enemy Manager")
    float SpawnTimer = 0.0f;

private:
    /** Static reference to singleton instance */
    static AEnemyManagerActor* SingletonInstance;

    /** Update enemy spawning */
    void UpdateEnemySpawning(float DeltaTime);

    /** Update enemy AI */
    void UpdateEnemyAI(float DeltaTime);

    /** Clean up destroyed enemies */
    void CleanupDestroyedEnemies();

    /** Get random spawn location around arena */
    FVector GetRandomSpawnLocation() const;

    /** Validate singleton instance */
    void ValidateSingletonInstance();
};
