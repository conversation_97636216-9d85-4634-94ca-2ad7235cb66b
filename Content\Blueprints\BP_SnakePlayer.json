{"BlueprintType": "Actor", "Name": "BP_SnakePlayer", "Description": "Basic Snake Player for Testing", "Components": [{"Type": "StaticMeshComponent", "Name": "SnakeHead", "Mesh": "Engine/BasicShapes/Cube", "Material": "Engine/BasicShapes/BasicShapeMaterial", "Scale": [0.5, 0.5, 0.5], "Color": [0, 1, 0, 1]}, {"Type": "MovementComponent", "Name": "Movement", "MaxSpeed": 300.0, "Acceleration": 1000.0}], "Variables": [{"Name": "CurrentDirection", "Type": "Vector", "Default": [1, 0, 0]}, {"Name": "MoveSpeed", "Type": "Float", "Default": 200.0}, {"Name": "TailSegments", "Type": "<PERSON><PERSON>y<Actor>", "Default": []}], "Events": [{"Name": "BeginPlay", "Actions": ["SetActorLocation(0, 0, 50)", "StartMovementTimer()"]}, {"Name": "InputAction_MoveUp", "Actions": ["SetCurrentDirection(0, 1, 0)"]}, {"Name": "InputAction_MoveDown", "Actions": ["SetCurrentDirection(0, -1, 0)"]}, {"Name": "InputAction_MoveLeft", "Actions": ["SetCurrentDirection(-1, 0, 0)"]}, {"Name": "InputAction_MoveRight", "Actions": ["SetCurrentDirection(1, 0, 0)"]}]}