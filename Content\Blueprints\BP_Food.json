{"BlueprintType": "Actor", "Name": "BP_Food", "Description": "Food pickups for Snake", "Components": [{"Type": "StaticMeshComponent", "Name": "FoodMesh", "Mesh": "Engine/BasicShapes/Sphere", "Material": "Engine/BasicShapes/BasicShapeMaterial", "Scale": [0.3, 0.3, 0.3], "Color": [1, 0, 0, 1]}, {"Type": "SphereCollisionComponent", "Name": "CollisionSphere", "Radius": 25.0}], "Variables": [{"Name": "PointValue", "Type": "Integer", "Default": 10}, {"Name": "PowerupType", "Type": "String", "Default": "Basic"}], "Events": [{"Name": "BeginPlay", "Actions": ["SpawnAtRandomLocation()", "StartRotationAnimation()"]}, {"Name": "OnOverlapBegin", "Actions": ["AddScoreToPlayer(PointValue)", "TriggerPowerupEffect(PowerupType)", "SpawnNewFood()", "DestroyActor()"]}]}