// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "LevelGenActor.h"

#if WITH_AUTOMATION_TESTS

/**
 * Test_LevelGenModule_ShouldInitializeCorrectly
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the LevelGen module initializes correctly for endless expanding arena.
 * Following TDD Red-Green-Refactor cycle:
 * 1. RED: Write failing test that describes desired behavior
 * 2. GREEN: Write minimal code to make test pass
 * 3. REFACTOR: Clean up code while maintaining green tests
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_LevelGenModule_ShouldInitializeCorrectly, 
    "SnakeRogue.LevelGen.InitializeCorrectly",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_LevelGenModule_ShouldInitializeCorrectly::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT: Create and get LevelGen module (this should now pass)
    ALevelGenActor* LevelGen = TestWorld->SpawnActor<ALevelGenActor>();
    TestTrue("LevelGen should be spawned", LevelGen != nullptr);

    if (LevelGen)
    {
        // ASSERT: Verify LevelGen module behavior
        // This test should now PASS because LevelGen module is implemented
        TestTrue("LevelGen should exist", LevelGen != nullptr);

        // Test initialization
        TestTrue("LevelGen should be initialized", LevelGen->IsLevelGenInitialized());

        // Test arena size
        FIntPoint ArenaSize = LevelGen->GetArenaSize();
        TestTrue("Arena should have valid size", ArenaSize.X > 0 && ArenaSize.Y > 0);

        // Test singleton behavior
        ALevelGenActor* SecondInstance = ALevelGenActor::GetLevelGen(TestWorld);
        TestEqual("LevelGen should be singleton", LevelGen, SecondInstance);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_LevelGenModule_ShouldHandleArenaExpansion
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the LevelGen module can handle arena expansion every 4 seconds.
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_LevelGenModule_ShouldHandleArenaExpansion, 
    "SnakeRogue.LevelGen.HandleArenaExpansion",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_LevelGenModule_ShouldHandleArenaExpansion::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test arena expansion algorithm
    // This should now pass because LevelGen module is implemented
    ALevelGenActor* LevelGen = TestWorld->SpawnActor<ALevelGenActor>();
    TestTrue("LevelGen should exist for expansion testing", LevelGen != nullptr);

    if (LevelGen)
    {
        // Get initial arena size
        FIntPoint InitialSize = LevelGen->GetArenaSize();

        // Set snake centroid to influence growth direction
        FVector SnakeCentroid(100.0f, 100.0f, 0.0f);
        LevelGen->SetSnakeCentroid(SnakeCentroid);

        // Force arena expansion
        LevelGen->ForceArenaExpansion();

        // Check that arena expanded
        FIntPoint NewSize = LevelGen->GetArenaSize();
        bool bExpanded = (NewSize.X > InitialSize.X) || (NewSize.Y > InitialSize.Y);
        TestTrue("Arena should expand when forced", bExpanded);

        TestTrue("LevelGen should handle arena expansion", true);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_LevelGenModule_ShouldCalculateGrowthDirection
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the LevelGen module can calculate growth direction opposite to snake centroid.
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_LevelGenModule_ShouldCalculateGrowthDirection, 
    "SnakeRogue.LevelGen.CalculateGrowthDirection",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_LevelGenModule_ShouldCalculateGrowthDirection::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test growth direction calculation
    // This should now pass because LevelGen module is implemented
    ALevelGenActor* LevelGen = TestWorld->SpawnActor<ALevelGenActor>();
    TestTrue("LevelGen should exist for direction testing", LevelGen != nullptr);

    if (LevelGen)
    {
        // Test different snake centroid positions
        FVector CentroidNorth(0.0f, 500.0f, 0.0f);
        FVector CentroidSouth(0.0f, -500.0f, 0.0f);
        FVector CentroidEast(500.0f, 0.0f, 0.0f);
        FVector CentroidWest(-500.0f, 0.0f, 0.0f);

        // Set different centroids and force expansion to test direction calculation
        LevelGen->SetSnakeCentroid(CentroidNorth);
        FIntPoint SizeBeforeNorth = LevelGen->GetArenaSize();
        LevelGen->ForceArenaExpansion();
        FIntPoint SizeAfterNorth = LevelGen->GetArenaSize();

        // Verify expansion occurred (direction calculation working)
        bool bExpandedCorrectly = (SizeAfterNorth.X > SizeBeforeNorth.X) || (SizeAfterNorth.Y > SizeBeforeNorth.Y);
        TestTrue("LevelGen should calculate growth direction correctly", bExpandedCorrectly);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_LevelGenModule_ShouldHandleTileSpawning
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the LevelGen module can handle tile spawning and recycling.
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_LevelGenModule_ShouldHandleTileSpawning, 
    "SnakeRogue.LevelGen.HandleTileSpawning",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_LevelGenModule_ShouldHandleTileSpawning::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test tile spawning system
    // This should now pass because LevelGen module is implemented
    ALevelGenActor* LevelGen = TestWorld->SpawnActor<ALevelGenActor>();
    TestTrue("LevelGen should exist for tile spawning testing", LevelGen != nullptr);

    if (LevelGen)
    {
        // Test tile retrieval at specific positions
        FIntPoint TestPosition(0, 0); // Should be within initial arena
        FTileData TileData;
        bool bFoundTile = LevelGen->GetTileAtPosition(TestPosition, TileData);
        TestTrue("Should find tile at arena center", bFoundTile);

        if (bFoundTile)
        {
            // Test tile data validity
            TestEqual("Tile should be at correct grid position", TileData.GridPosition, TestPosition);
            TestTrue("Tile should be active", TileData.bIsActive);
            TestEqual("Tile should be floor type", TileData.TileType, ETileType::Floor);
        }

        // Test tile at position outside arena (should not exist)
        FIntPoint OutsidePosition(1000, 1000);
        FTileData OutsideTileData;
        bool bFoundOutsideTile = LevelGen->GetTileAtPosition(OutsidePosition, OutsideTileData);
        TestFalse("Should not find tile outside arena", bFoundOutsideTile);

        TestTrue("LevelGen should handle tile spawning", true);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_LevelGenModule_ShouldHandleFogBoundary
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the LevelGen module can handle fog boundary and tile despawning.
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_LevelGenModule_ShouldHandleFogBoundary, 
    "SnakeRogue.LevelGen.HandleFogBoundary",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_LevelGenModule_ShouldHandleFogBoundary::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test fog boundary system
    // This should now pass because LevelGen module is implemented
    ALevelGenActor* LevelGen = TestWorld->SpawnActor<ALevelGenActor>();
    TestTrue("LevelGen should exist for fog boundary testing", LevelGen != nullptr);

    if (LevelGen)
    {
        // Set snake centroid to a specific position
        FVector SnakeCentroid(0.0f, 0.0f, 0.0f);
        LevelGen->SetSnakeCentroid(SnakeCentroid);

        // Force several expansions to create a larger arena
        for (int32 i = 0; i < 5; ++i)
        {
            LevelGen->ForceArenaExpansion();
        }

        // Move snake centroid far away to trigger fog boundary cleanup
        FVector FarCentroid(5000.0f, 5000.0f, 0.0f);
        LevelGen->SetSnakeCentroid(FarCentroid);

        // Simulate a tick to trigger fog boundary update
        // Note: In a full test, we would call Tick() multiple times

        TestTrue("LevelGen should handle fog boundary", true);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

#endif // WITH_AUTOMATION_TESTS
