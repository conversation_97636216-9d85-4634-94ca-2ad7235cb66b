# Game Design Document – **Snake Rogue**

*Version 0.1 • Date 2025-06-13*

## 1. Game Pillars
1. **Momentum** – you are always growing, the arena is always expanding; motion never stops.
2. **Chaos & Mastery** – dozens of synergistic power‑ups and punishing debuffs create delightful mayhem but reward practiced control.
3. **Replay Forever** – meta‑progression perks + cosmetics ensure “just one more run.”

## 2. Player Journey (Narrative‑Lite)
> *“I boot up Snake Rogue and the title splashes neon‑ink onto the screen…”*

### First Launch Walk‑through
1. Title → Start → humorous AI narrator explains controls in <20 s (“Left stick slither, right stick snack.”).
2. Tutorial micro‑arena shows off:
   * basic turning,
   * first **Speed‑Up** buff (instant),
   * first **Debuff** (Slow Goo) to contrast.
3. End tutorial → **Run #1**.

### Core Gameplay Loop
`Start Run → Collect food → Tail grows → Arena expands → Handle hazards → Collect buffs/debuffs → Score & unlocks → Death → Spend meta‑currency → Start next run`.

*Death is inevitable*. Score = total tiles traversed × combo multiplier × difficulty tier.

### Multi‑Run Progression
* **Perk Tree** – persistent upgrades (e.g., +5 % base speed).
* **Cosmetics** – skins, trails, audio packs, camera skins.
* **Hidden Lore** – every 10th run unlocks a lore vignette + silly cut‑scene.

## 3. Game World
*Isometric diorama* floating in abstract void; walls and obstacles are low‑poly, hi‑shine. Beyond walls: colored fog indicating expansion direction.

## 4. Systems Detail
### 4.1 Snake Movement
*Classic tile snap* with bezier interpolation; occasional *“Slither Surge”* events let you move fully analog for 3 s.

### 4.2 Dynamic Camera
* Third‑person isometric (~35° tilt).
* Spring arm tether; collisions & boosts apply impulse to camera rig → visible lag & shake.
* FOV zooms by +10 % on speed buffs.

### 4.3 Procedural Arena Growth
* Every 4 seconds, compute direction opposite current centroid → extrude new 3‑tile‑wide ring on that side.
* Oldest tiles despawn behind fog (endless illusion).

### 4.4 Power‑Ups (Buff Families)
| Family | Example | Trigger | Variables |
|--------|---------|---------|-----------|
| Speed‑Up | Turbo Mango | Instant | +200 % speed, 5 s |
| Slow‑Motion | Time Syrup | Player‑triggered | -75 % world speed, 8 s |
| Elemental | Frost Fang | Instant | Projectile cone, 12 damage |
| Teleport | Warp Blink | Player‑triggered | 5 tiles forward |
| Non‑Elemental | Tail‑Slam | Player | 360° melee, knockback |
| Wall‑Breaker | Ghost Strike | Instant | Pass through 1 wall |
| Invincibility | Star Glide | Instant | 7 s, deals 3 damage on contact |
| Shields | Bubble Skin | Instant | Absorb 1 hit, 15 s timeout |
| Transparency | Phase Slip | Player | 10 s no collisions |
| Mines | Scale Charge | Player | Drop 3 mines, 2 s fuse |

**Total planned buffs:** 48 (6 per family × 8 families).

### 4.5 Debuffs (Opposing & Novel)
| Debuff | Effect |
|--------|--------|
| Sticky Mire | -50 % speed, 6 s |
| Reverse Controls | Invert left/right, 4 s |
| Poison Chomp | -1 HP per sec, 8 s |
| Tail‑Fragility | Tail sections break off on impact |
| Blind Spot | Camera FOV shrinks 20 % |
| Random Teleport | Teleport to random tile |
| EMP | Disables power‑up usage 5 s |

**Total planned debuffs:** 32.

### 4.6 Enemies & Hazards
1. **Wall Turrets** – shoot projectiles in lines.
2. **Chasers** – AI snakes.
3. **Static Mines** – explode on proximity.
4. **Moving Sawblades** – patrol rails.
5. **Environmental Traps** – spike rows, laser grids.

### 4.7 Multiplayer Modes
* **Co‑op** – shared tail, joint score.
* **Versus** – snakes collide, last alive wins.
* **Online PvP** – deterministic lock‑step.

## 5. Art & Audio
* **Art Styles (Unlockable)**: Neon Synthwave • Pastel Candy • Bioluminescent Jungle • Vaporwave • Retro CRT.
* **Graphics Features**: Bloom, Niagara FX, dynamic depth‑of‑field.
* **Audio Themes**: Chiptune EDM base; each art style swaps audio palette.

## 6. Story & Humor
Simple premise: *You’re an experimental cyber‑serpent in an ever‑expanding holo‑arena.* Hidden logs reveal satirical “corporate training” backstory.

## 7. Accessibility
Color‑blind palettes, speed‑assist mode, one‑hand controls, adjustable camera shake.

## 8. Monetization
None – free itch.io release.

## 9. Glossary
* **Buff** – temporary beneficial effect.
* **Debuff** – temporary detrimental effect.

--
