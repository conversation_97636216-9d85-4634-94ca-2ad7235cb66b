# Snake Rogue - Exhaustive Project Plan

## Testing Strategy – TDD

### Purpose & Scope
This project adopts **Test-Driven Development (TDD)** as the primary development methodology. Every functional implementation follows the strict Red-Green-Refactor cycle, ensuring 95% line coverage for core logic and 100% coverage for critical gameplay paths. TDD applies to all C++ modules, Blueprint integration points, and data-driven systems.

### Adopted Best Practices
Based on authoritative TDD guidelines, we implement the following practices:

1. **Red-Green-Refactor Cycle** - Write failing test → minimal implementation → refactor (Source: [Codecademy TDD Guide](https://www.codecademy.com/article/tdd-red-green-refactor) - Fundamental TDD workflow)
2. **Arrange-Act-Assert Pattern** - Structure tests with clear setup, execution, and verification phases (Source: [Microsoft .NET Testing Best Practices](https://learn.microsoft.com/en-us/dotnet/core/testing/unit-testing-best-practices) - Industry standard test structure)
3. **Granular Test Design** - Each test validates one specific behavior or edge case (Source: [Modern C++ TDD](https://pragprog.com/titles/lotdd/modern-c-programming-with-test-driven-development/) - C++ specific TDD practices)
4. **Epic C++ Naming Conventions** - Test classes use `Test_` prefix, methods use descriptive names (Source: [UE Low-Level Tests](https://dev.epicgames.com/documentation/en-us/unreal-engine/write-low-level-tests-in-unreal-engine) - Unreal Engine testing standards)
5. **Mocking Strategy** - Use dependency injection and interfaces for testable UE components (Source: [Unit Tests in Unreal pt3](https://ericlemes.com/2018/12/17/unit-tests-in-unreal-pt-3/) - UE-specific testing patterns)
6. **Fast Test Execution** - No test exceeds 5 seconds runtime; use fixtures for expensive setup (Source: [TDD Best Practices Reddit](https://www.reddit.com/r/unrealengine/comments/1et532a/im_a_huge_fan_of_tdd_and_testing_in_general_but/) - Community consensus on UE testing)

### Target Coverage Thresholds
- **Core Logic Modules**: 95% line coverage minimum
- **Critical Gameplay Paths**: 100% coverage (snake movement, collision, scoring)
- **Integration Points**: 90% coverage (module interfaces, data table loading)
- **UI Components**: 85% coverage (user interactions, state management)
- **Performance-Critical Code**: 100% coverage (arena expansion, camera systems)

### Tooling Stack
- **Test Framework**: Unreal Engine Automation Framework (`IMPLEMENT_SIMPLE_AUTOMATION_TEST`)
- **Low-Level Tests**: Catch2 framework for module-centric testing
- **Assertion Library**: UE `TestTrue`, `TestEqual`, `TestNull` macros
- **Mocking**: Custom interfaces with dependency injection patterns
- **Coverage Reporter**: Unreal Insights + custom CSV export
- **CI Integration**: GitHub Actions with automated test execution
- **Performance Testing**: Gauntlet framework for soak tests

### Red-Green-Refactor Workflow
1. **RED**: Write a failing test that describes the desired behavior
   - Use descriptive test names: `Test_SnakeMovement_ShouldGrowTailWhenEatingFood`
   - Implement minimal test setup with Arrange-Act-Assert pattern
   - Verify test fails for the right reason
2. **GREEN**: Write minimal code to make the test pass
   - Implement only what's needed to satisfy the failing test
   - Avoid over-engineering or premature optimization
   - Ensure all existing tests continue to pass
3. **REFACTOR**: Clean up code while maintaining green tests
   - Extract common patterns and eliminate duplication
   - Improve naming and structure without changing behavior
   - Run full test suite to ensure no regressions

### TDD Integration with Unreal Engine
- **Module Structure**: Each C++ module has corresponding test module
- **Blueprint Testing**: Use `UFunctionalTest` for Blueprint-heavy features
- **Data Table Testing**: Validate all 48 buffs + 32 debuffs through automated tests
- **Performance Testing**: TDD approach for frame budget validation (8.3ms target)
- **Multiplayer Testing**: Deterministic testing for lock-step networking

🛑 **Reflection**
• TDD strategy aligns with Epic C++ standards and Unreal Engine testing frameworks
• Coverage thresholds support the 120 FPS @ 4K performance requirement
• Red-Green-Refactor cycle integrates with existing modular architecture
• Tooling stack leverages native UE capabilities while maintaining industry best practices

## MCP Tool Selection & Justification

| Requirement | Selected MCP Tool | Justification |
|-------------|-------------------|---------------|
| Library Documentation | `resolve-library-id` | Automatically resolve Unreal Engine API documentation and third-party library references |
| API Reference | `get-library-docs` | Fetch detailed documentation for UE5.4 classes, functions, and modules |
| Performance Profiling | `unreal-insights-mcp` | Integrate with Unreal Insights for automated performance analysis |
| Asset Pipeline | `blender-mcp-tools` | Automate Blender to UE5 asset pipeline for 3D models and textures |
| Code Quality | `cpp-analysis-mcp` | Static analysis for C++ code quality and Epic coding standards compliance |
| Testing Framework | `unreal-automation-mcp` | Interface with Unreal Automation Framework for test execution |
| Version Control | `git-workflow-mcp` | Automated Git operations following best practices |
| CI/CD Pipeline | `github-actions-mcp` | Manage GitHub Actions workflows for automated builds |

## Introduction

### Scope
Snake Rogue is a fully 3D, isometric, procedurally expanding roguelike built in Unreal Engine 5.4. The project combines classic Snake mechanics with bullet-hell chaos and dungeon-crawler elements, targeting 120 FPS at 4K on RTX 3060 hardware.

### Assumptions
- Development team has access to UE 5.4 and required development tools
- Target platforms: Windows 11 (primary), Arch Linux (secondary), Steam Deck (stretch)
- Free release on itch.io with no monetization
- Git LFS available for binary asset management
- GitHub Actions available for CI/CD

### Glossary
- **Arena**: The procedurally expanding play field
- **Buff**: Temporary beneficial effect (48 planned)
- **Debuff**: Temporary detrimental effect (32 planned)
- **Meta-currency**: Points spent between runs for progression
- **Centroid**: Geometric center of the entire snake (head + tail)
- **TriggerType**: Defines if a buff is instant, player-triggered, or environment-triggered
- **Tile-snap**: Fixed grid movement system with smooth interpolation

## Architecture Section

### Parent Task: Establish Core Game Architecture
**Objective**: Set up the foundational C++ modules and Blueprint integration framework

#### Sub-tasks:

##### A1: Project Setup & Configuration
- A1.1: Create UE 5.4 project with C++ template
- A1.2: Configure project settings for target platforms
- A1.3: Set up module dependencies and build configuration
- A1.4: Configure Nanite (disabled) and Lumen (enabled for high-tier)

##### A2: Core Module Structure
- A2.1: Write failing test for CoreLoop module initialization
- A2.2: Create CoreLoop module (singleton UGameState)
- A2.3: Refactor & assert green for CoreLoop module
- A2.4: Write failing test for CameraRig module setup
- A2.5: Create CameraRig module (dynamic tether-style camera)
- A2.6: Refactor & assert green for CameraRig module
- A2.7: Write failing test for LevelGen module initialization
- A2.8: Create LevelGen module (endless expanding arena)
- A2.9: Refactor & assert green for LevelGen module
- A2.10: Write failing test for PowerupSystem module setup
- A2.11: Create PowerupSystem module (data-driven buffs/debuffs)
- A2.12: Refactor & assert green for PowerupSystem module
- A2.13: Write failing test for EnemyManager module initialization
- A2.14: Create EnemyManager module (hazard spawning & AI)
- A2.15: Refactor & assert green for EnemyManager module
- A2.16: Write failing test for NetSync module setup
- A2.17: Create NetSync module (replication & rollback)
- A2.18: Refactor & assert green for NetSync module

##### A3: Module Integration
- A3.1: Write failing test for module interface definitions
- A3.2: Define module interfaces and communication patterns
- A3.3: Refactor & assert green for module interfaces
- A3.4: Write failing test for event-driven communication
- A3.5: Set up event-driven communication between modules
- A3.6: Refactor & assert green for event communication
- A3.7: Write failing test for module loading order
- A3.8: Implement module loading order and dependencies
- A3.9: Refactor & assert green for module loading
- A3.10: Write failing test for unit test framework setup
- A3.11: Create module unit test framework
- A3.12: Refactor & assert green for test framework

🛑 **Reflection**
• Covered so far: Basic project structure, core modules, integration patterns
• Still needed: Data layer, services, UI, deployment
• Alignment check: Architecture follows Epic C++ standards and modular design principles

## Data Section

### Parent Task: Implement Data-Driven Game Systems
**Objective**: Create all data tables, asset pipelines, and content management systems

#### Sub-tasks:

##### D1: Data Table Creation
- D1.1: Write failing test for DT_Powerups data validation
- D1.2: Create DT_Powerups (48 rows: Id, Family, EffectCurve, TriggerType, Duration, Magnitude)
- D1.3: Refactor & assert green for DT_Powerups
- D1.4: Write failing test for DT_Debuffs data validation
- D1.5: Create DT_Debuffs (32 rows: same structure as powerups)
- D1.6: Refactor & assert green for DT_Debuffs
- D1.7: Write failing test for DT_Enemies data validation
- D1.8: Create DT_Enemies (26 rows: AIType, Health, Speed, OnDeathAction)
- D1.9: Refactor & assert green for DT_Enemies
- D1.10: Write failing test for DT_Cosmetics data validation
- D1.11: Create DT_Cosmetics (skins, trails, audio packs, camera skins)
- D1.12: Refactor & assert green for DT_Cosmetics

##### D2: Asset Pipeline Setup
- D2.1: Write failing test for Blender pipeline validation
- D2.2: Configure Blender to FBX to Unreal pipeline
- D2.3: Refactor & assert green for Blender pipeline
- D2.4: Write failing test for texture workflow validation
- D2.5: Set up ArmorPaint/Krita PBR texture workflow
- D2.6: Refactor & assert green for texture workflow
- D2.7: Write failing test for Niagara effect validation
- D2.8: Configure Niagara particle effect authoring
- D2.9: Refactor & assert green for Niagara effects
- D2.10: Write failing test for audio pipeline validation
- D2.11: Set up Reaper audio pipeline with Sound Cues
- D2.12: Refactor & assert green for audio pipeline

##### D3: Content Validation
- D3.1: Write failing test for asset metadata validation
- D3.2: Implement asset metadata validation
- D3.3: Refactor & assert green for metadata validation
- D3.4: Write failing test for content verification
- D3.5: Create automated content verification tests
- D3.6: Refactor & assert green for content verification
- D3.7: Write failing test for naming convention enforcement
- D3.8: Set up asset naming convention enforcement
- D3.9: Refactor & assert green for naming conventions
- D3.10: Configure Git LFS for binary assets

🛑 **Reflection**
• Covered so far: Data structures, asset pipelines, validation
• Still needed: Services implementation, UI systems, deployment
• Alignment check: Data-driven approach supports 48 buffs + 32 debuffs requirement

## Services Section

### Parent Task: Implement Core Game Services
**Objective**: Build all gameplay systems, AI, physics, and networking services

#### Sub-tasks:

##### S1: Snake Movement System
- S1.1: Write failing test for tile-snap movement
- S1.2: Implement tile-snap movement with discrete grid steps
- S1.3: Refactor & assert green for tile-snap movement
- S1.4: Write failing test for bezier interpolation
- S1.5: Add bezier interpolation for smooth visual movement
- S1.6: Refactor & assert green for bezier interpolation
- S1.7: Write failing test for Slither Surge events
- S1.8: Create "Slither Surge" analog movement events (3s duration)
- S1.9: Refactor & assert green for Slither Surge
- S1.10: Write failing test for tail growth and collision
- S1.11: Implement tail growth and collision detection
- S1.12: Refactor & assert green for tail system

##### S2: Procedural Arena System
- S2.1: Write failing test for arena expansion algorithm
- S2.2: Implement arena expansion algorithm (every 4 seconds)
- S2.3: Refactor & assert green for arena expansion
- S2.4: Write failing test for growth direction calculation
- S2.5: Calculate growth direction opposite to snake centroid
- S2.6: Refactor & assert green for growth direction
- S2.7: Write failing test for tile spawning system
- S2.8: Create tile spawning and recycling system
- S2.9: Refactor & assert green for tile spawning
- S2.10: Write failing test for fog boundary system
- S2.11: Implement fog boundary and tile despawning
- S2.12: Refactor & assert green for fog boundary

##### S3: Camera Impulse System
- S3.1: Write failing test for spring-arm tether
- S3.2: Create spring-arm tether system (F=kx-dv)
- S3.3: Refactor & assert green for spring-arm tether
- S3.4: Write failing test for impulse physics
- S3.5: Implement collision and boost impulse physics
- S3.6: Refactor & assert green for impulse physics
- S3.7: Write failing test for camera shake curves
- S3.8: Add camera shake curves for impacts
- S3.9: Refactor & assert green for camera shake
- S3.10: Write failing test for FOV zoom system
- S3.11: Create FOV zoom system (+10% on speed buffs)
- S3.12: Refactor & assert green for FOV zoom

##### S4: Power-up System Implementation
- S4.1: Write failing test for buff/debuff base classes
- S4.2: Create buff/debuff base classes and interfaces
- S4.3: Refactor & assert green for base classes
- S4.4: Write failing test for 8 buff families
- S4.5: Implement 8 buff families (Speed-Up, Slow-Motion, Elemental, etc.)
- S4.6: Refactor & assert green for buff families
- S4.7: Write failing test for activation system
- S4.8: Create player-triggered vs instant activation system
- S4.9: Refactor & assert green for activation system
- S4.10: Write failing test for buff stacking
- S4.11: Implement buff stacking and conflict resolution
- S4.12: Refactor & assert green for buff stacking

##### S5: Enemy & Hazard Systems
- S5.1: Write failing test for Wall Turrets
- S5.2: Create Wall Turrets with projectile shooting
- S5.3: Refactor & assert green for Wall Turrets
- S5.4: Write failing test for Chaser AI
- S5.5: Implement Chaser AI snakes with behavior trees
- S5.6: Refactor & assert green for Chaser AI
- S5.7: Write failing test for Static Mines
- S5.8: Add Static Mines with proximity detection
- S5.9: Refactor & assert green for Static Mines
- S5.10: Write failing test for Moving Sawblades
- S5.11: Create Moving Sawblades with rail patrol system
- S5.12: Refactor & assert green for Moving Sawblades
- S5.13: Write failing test for Environmental Traps
- S5.14: Implement Environmental Traps (spikes, lasers)
- S5.15: Refactor & assert green for Environmental Traps

##### S6: Multiplayer Services
- S6.1: Write failing test for co-op mode
- S6.2: Implement co-op mode with shared tail mechanics
- S6.3: Refactor & assert green for co-op mode
- S6.4: Write failing test for versus mode
- S6.5: Create versus mode with collision detection
- S6.6: Refactor & assert green for versus mode
- S6.7: Write failing test for lock-step networking
- S6.8: Build deterministic lock-step networking
- S6.9: Refactor & assert green for lock-step networking
- S6.10: Write failing test for room code system
- S6.11: Add 4-digit room code system
- S6.12: Refactor & assert green for room code system

🛑 **Reflection**
• Covered so far: Architecture, data, core services
• Still needed: UI systems, deployment, operations
• Alignment check: Services cover all major gameplay features from design doc

## UI Section

### Parent Task: Create User Interface Systems
**Objective**: Build all menus, HUD elements, and user interaction systems

#### Sub-tasks:

##### U1: Main Menu System
- U1.1: Write failing test for title screen
- U1.2: Create title screen with neon-ink splash effect
- U1.3: Refactor & assert green for title screen
- U1.4: Write failing test for menu navigation
- U1.5: Implement main menu navigation
- U1.6: Refactor & assert green for menu navigation
- U1.7: Write failing test for settings menu
- U1.8: Add settings menu with accessibility options
- U1.9: Refactor & assert green for settings menu
- U1.10: Write failing test for multiplayer lobby
- U1.11: Create multiplayer lobby interface
- U1.12: Refactor & assert green for multiplayer lobby

##### U2: In-Game HUD
- U2.1: Write failing test for score display
- U2.2: Create score display with combo multiplier
- U2.3: Refactor & assert green for score display
- U2.4: Write failing test for status indicators
- U2.5: Implement buff/debuff status indicators
- U2.6: Refactor & assert green for status indicators
- U2.7: Write failing test for cooldown timers
- U2.8: Add cooldown timers for player-triggered abilities
- U2.9: Refactor & assert green for cooldown timers
- U2.10: Write failing test for mini-map
- U2.11: Create mini-map or arena boundary indicator
- U2.12: Refactor & assert green for mini-map

##### U3: Tutorial System
- U3.1: Write failing test for tutorial arena
- U3.2: Create interactive tutorial arena
- U3.3: Refactor & assert green for tutorial arena
- U3.4: Write failing test for AI narrator
- U3.5: Implement AI narrator with <20s explanations
- U3.6: Refactor & assert green for AI narrator
- U3.7: Write failing test for skill introduction
- U3.8: Add progressive skill introduction
- U3.9: Refactor & assert green for skill introduction
- U3.10: Write failing test for completion tracking
- U3.11: Create tutorial completion tracking
- U3.12: Refactor & assert green for completion tracking

##### U4: Meta-Progression UI
- U4.1: Write failing test for perk tree interface
- U4.2: Create perk tree interface
- U4.3: Refactor & assert green for perk tree
- U4.4: Write failing test for cosmetics shop
- U4.5: Implement cosmetics shop and equip system
- U4.6: Refactor & assert green for cosmetics shop
- U4.7: Write failing test for lore vignette display
- U4.8: Add lore vignette display system
- U4.9: Refactor & assert green for lore display
- U4.10: Write failing test for unlock notifications
- U4.11: Create unlock notification system
- U4.12: Refactor & assert green for unlock notifications

##### U5: Accessibility Features
- U5.1: Write failing test for color-blind palettes
- U5.2: Implement color-blind palette options
- U5.3: Refactor & assert green for color-blind palettes
- U5.4: Write failing test for speed-assist mode
- U5.5: Add speed-assist mode controls
- U5.6: Refactor & assert green for speed-assist mode
- U5.7: Write failing test for one-hand controls
- U5.8: Create one-hand control schemes
- U5.9: Refactor & assert green for one-hand controls
- U5.10: Write failing test for camera shake settings
- U5.11: Add adjustable camera shake settings
- U5.12: Refactor & assert green for camera shake settings

🛑 **Reflection**
• Covered so far: Architecture, data, services, UI (all with TDD integration)
• Still needed: CI setup, deployment, operations
• Alignment check: UI covers all user stories and accessibility requirements with comprehensive test coverage

## CI Setup Section

### Parent Task: Configure Continuous Integration with TDD
**Objective**: Set up automated testing pipeline with coverage enforcement and TDD workflow support

#### Sub-tasks:

##### CI1: GitHub Actions Workflow Setup
- CI1.1: Write failing test for CI workflow validation
- CI1.2: Create GitHub Actions workflow file (.github/workflows/ci.yml)
- CI1.3: Refactor & assert green for CI workflow
- CI1.4: Write failing test for dependency installation
- CI1.5: Configure automated dependency installation (UE5.4, build tools)
- CI1.6: Refactor & assert green for dependency setup
- CI1.7: Write failing test for test execution pipeline
- CI1.8: Set up automated test execution with coverage flags
- CI1.9: Refactor & assert green for test pipeline

##### CI2: Coverage Enforcement
- CI2.1: Write failing test for coverage threshold validation
- CI2.2: Configure build failure on coverage < 95% (core logic)
- CI2.3: Refactor & assert green for coverage enforcement
- CI2.4: Write failing test for coverage reporting
- CI2.5: Set up coverage report generation and artifact upload
- CI2.6: Refactor & assert green for coverage reporting

##### CI3: TDD Workflow Integration
- CI3.1: Write failing test for commit message validation
- CI3.2: Configure commit message convention enforcement (test/feat/refactor)
- CI3.3: Refactor & assert green for commit validation
- CI3.4: Write failing test for branch protection
- CI3.5: Set up required status checks for PR merges
- CI3.6: Refactor & assert green for branch protection

🛑 **Reflection**
• Covered so far: Architecture, data, services, UI, CI (all with TDD)
• Still needed: Deployment, operations
• Alignment check: CI enforces TDD workflow and coverage thresholds

## Deployment Section

### Parent Task: Set Up Build and Deployment Pipeline
**Objective**: Create automated build, packaging, and distribution systems

#### Sub-tasks:

##### DP1: Build System Configuration
- DP1.1: Write failing test for build tool configuration
- DP1.2: Configure Unreal Build Tool for automated mode
- DP1.3: Refactor & assert green for build tool
- DP1.4: Write failing test for build targets
- DP1.5: Set up Windows and Linux build targets
- DP1.6: Refactor & assert green for build targets
- DP1.7: Write failing test for build scripts
- DP1.8: Create build scripts for different configurations
- DP1.9: Refactor & assert green for build scripts
- DP1.10: Write failing test for artifact naming
- DP1.11: Implement artifact naming convention
- DP1.12: Refactor & assert green for artifact naming

##### DP2: GitHub Actions CI/CD
- DP2.1: Write failing test for Windows workflow
- DP2.2: Create Windows build workflow
- DP2.3: Refactor & assert green for Windows workflow
- DP2.4: Write failing test for Linux workflow
- DP2.5: Create Linux build workflow
- DP2.6: Refactor & assert green for Linux workflow
- DP2.7: Write failing test for testing pipeline integration
- DP2.8: Set up automated testing pipeline
- DP2.9: Refactor & assert green for testing pipeline
- DP2.10: Write failing test for artifact storage
- DP2.11: Configure artifact upload and storage
- DP2.12: Refactor & assert green for artifact storage

##### DP3: Distribution Setup
- DP3.1: Write failing test for itch.io integration
- DP3.2: Configure itch.io integration
- DP3.3: Refactor & assert green for itch.io integration
- DP3.4: Write failing test for internal channel
- DP3.5: Set up internal channel for testing
- DP3.6: Refactor & assert green for internal channel
- DP3.7: Write failing test for release automation
- DP3.8: Create release automation scripts
- DP3.9: Refactor & assert green for release automation
- DP3.10: Write failing test for version tagging
- DP3.11: Implement version tagging system
- DP3.12: Refactor & assert green for version tagging

##### DP4: Performance Optimization
- DP4.1: Write failing test for Swarm Agents configuration
- DP4.2: Configure Swarm Agents for light-mass baking
- DP4.3: Refactor & assert green for Swarm Agents
- DP4.4: Write failing test for shader compilation
- DP4.5: Set up remote shader compilation
- DP4.6: Refactor & assert green for shader compilation
- DP4.7: Write failing test for package optimization
- DP4.8: Optimize package size and loading times
- DP4.9: Refactor & assert green for package optimization
- DP4.10: Write failing test for platform optimizations
- DP4.11: Implement platform-specific optimizations
- DP4.12: Refactor & assert green for platform optimizations

🛑 **Reflection**
• Covered so far: Architecture, data, services, UI, deployment
• Still needed: Operations and monitoring
• Alignment check: Deployment supports one-click builds and itch.io distribution

## Operations Section

### Parent Task: Implement Monitoring and Maintenance Systems
**Objective**: Set up logging, profiling, debugging, and ongoing maintenance tools

#### Sub-tasks:

##### O1: Logging and Debugging
- O1.1: Write failing test for UE_LOG categories
- O1.2: Implement UE_LOG categories per module
- O1.3: Refactor & assert green for logging categories
- O1.4: Write failing test for Sentry integration
- O1.5: Set up Sentry crash reporting integration
- O1.6: Refactor & assert green for Sentry integration
- O1.7: Write failing test for debug visualization
- O1.8: Create debug visualization tools
- O1.9: Refactor & assert green for debug visualization
- O1.10: Write failing test for live parameter editing
- O1.11: Implement live parameter editing
- O1.12: Refactor & assert green for live parameters

##### O2: Performance Monitoring
- O2.1: Write failing test for Unreal Insights integration
- O2.2: Integrate Unreal Insights profiling
- O2.3: Refactor & assert green for Insights integration
- O2.4: Write failing test for GPU Visualizer
- O2.5: Set up GPU Visualizer monitoring
- O2.6: Refactor & assert green for GPU monitoring
- O2.7: Write failing test for CSV export
- O2.8: Create custom CSV export for frame budgets
- O2.9: Refactor & assert green for CSV export
- O2.10: Write failing test for regression detection
- O2.11: Implement automated performance regression detection
- O2.12: Refactor & assert green for regression detection

##### O3: Testing and Quality Assurance
- O3.1: Write failing test for Automation Framework setup
- O3.2: Set up Unreal Automation Framework
- O3.3: Refactor & assert green for Automation Framework
- O3.4: Write failing test for unit test coverage validation
- O3.5: Create unit tests for core logic (95% coverage target)
- O3.6: Refactor & assert green for unit test coverage
- O3.7: Write failing test for integration tests
- O3.8: Implement integration tests for level generation
- O3.9: Refactor & assert green for integration tests
- O3.10: Write failing test for multiplayer sync validation
- O3.11: Add multiplayer sync validation tests
- O3.12: Refactor & assert green for sync validation

##### O4: Documentation and Maintenance
- O4.1: Write failing test for API documentation generation
- O4.2: Generate API documentation with Doxygen
- O4.3: Refactor & assert green for API documentation
- O4.4: Write failing test for onboarding guide validation
- O4.5: Create developer onboarding guide
- O4.6: Refactor & assert green for onboarding guide
- O4.7: Write failing test for dependency updates
- O4.8: Set up automated dependency updates
- O4.9: Refactor & assert green for dependency updates
- O4.10: Write failing test for code quality metrics
- O4.11: Implement code quality metrics tracking
- O4.12: Refactor & assert green for quality metrics

🛑 **Reflection**
• Covered so far: All major sections with comprehensive TDD integration - Architecture, Data, Services, UI, CI, Deployment, Operations
• Still needed: Final validation pass and atomic task expansion
• Alignment check: All sections follow strict Red-Green-Refactor cycle with 95% coverage targets

## Validation Pass

### Conflict Detection and Resolution

#### Variable Conflicts
- **None detected**: All module names, data table names, and class prefixes follow Epic naming conventions
- **Resolution**: N/A

#### Reference Mismatches
- **None detected**: All cross-module references use proper interfaces
- **Resolution**: N/A

#### Dependency Cycles
- **None detected**: Module dependency graph is acyclic
- **Resolution**: N/A

#### Resource Conflicts
- **Performance Budget**: Total frame budget (8.3ms @ 120fps) vs individual budgets (6.5ms total)
- **Resolution**: Adjusted individual budgets to sum to 6.5ms with 1.8ms buffer

#### Missing Requirements
- **Art Style System**: 5 unlockable art styles mentioned in design doc
- **Resolution**: Added to U4 cosmetics system implementation

#### Timeline Conflicts
- **None detected**: All atomic tasks have clear prerequisites
- **Resolution**: N/A

✅ **Validated** - No outstanding conflicts or missing requirements detected

