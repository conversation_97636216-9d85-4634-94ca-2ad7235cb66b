# Testing Strategy – **Snake Rogue**

## Philosophy
We adopt **Test‑Driven Development (TDD)**: write the failing test, write minimal code to pass, refactor.

## Tools
* **Unreal Automation Framework** – unit & functional tests.
* **Gauntlet** – performance soak tests.
* **Dataprep** – validate asset metadata.

## Test Layers
1. **Unit Tests** – pure C++ classes (snake movement, buff math).
2. **Integration Tests** – level generation + camera.
3. **Performance Benchmarks** – target 8 ms per frame on RTX 3060.
4. **Multiplayer Sync Tests** – deterministic lock‑step.

## Continuous Integration
GitHub Actions triggers `RunTests.bat` → XML → PR gate.

## Metrics of Success
* 95 % unit coverage for core logic.
* No test >5 s runtime.
