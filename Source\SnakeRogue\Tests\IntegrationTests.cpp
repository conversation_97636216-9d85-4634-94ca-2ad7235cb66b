// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "CoreLoopGameState.h"
#include "CameraRigActor.h"
#include "LevelGenActor.h"
#include "PowerupSystemActor.h"
#include "EnemyManagerActor.h"
#include "NetSync/NetSyncActor.h"

#if WITH_AUTOMATION_TESTS

/**
 * INTEGRATION TEST - ALL MODULES WORKING TOGETHER!
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_FullSystemIntegration, 
    "SnakeRogue.Integration.FullSystem",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_FullSystemIntegration::RunTest(const FString& Parameters)
{
    // ARRANGE: Create test world
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT: Spawn ALL core modules
    ACoreLoopGameState* CoreLoop = TestWorld->SpawnActor<ACoreLoopGameState>();
    ACameraRigActor* CameraRig = TestWorld->SpawnActor<ACameraRigActor>();
    ALevelGenActor* LevelGen = TestWorld->SpawnActor<ALevelGenActor>();
    APowerupSystemActor* PowerupSystem = TestWorld->SpawnActor<APowerupSystemActor>();
    AEnemyManagerActor* EnemyManager = TestWorld->SpawnActor<AEnemyManagerActor>();
    ANetSyncActor* NetSync = TestWorld->SpawnActor<ANetSyncActor>();

    // ASSERT: All modules initialized
    TestTrue("CoreLoop initialized", CoreLoop && CoreLoop->AreCoreSystemsInitialized());
    TestTrue("CameraRig initialized", CameraRig && CameraRig->IsCameraRigInitialized());
    TestTrue("LevelGen initialized", LevelGen && LevelGen->IsLevelGenInitialized());
    TestTrue("PowerupSystem initialized", PowerupSystem && PowerupSystem->IsPowerupSystemInitialized());
    TestTrue("EnemyManager initialized", EnemyManager && EnemyManager->IsEnemyManagerInitialized());
    TestTrue("NetSync initialized", NetSync && NetSync->IsNetSyncInitialized());

    // ASSERT: Module communication works
    if (CoreLoop && LevelGen && CameraRig)
    {
        // Test arena expansion with camera following
        FVector SnakePosition(100.0f, 100.0f, 0.0f);
        LevelGen->SetSnakeCentroid(SnakePosition);
        CameraRig->SetFollowTarget(CoreLoop);
        
        FIntPoint InitialSize = LevelGen->GetArenaSize();
        LevelGen->ForceArenaExpansion();
        FIntPoint NewSize = LevelGen->GetArenaSize();
        
        TestTrue("Arena should expand", (NewSize.X > InitialSize.X) || (NewSize.Y > InitialSize.Y));
    }

    // ASSERT: Powerup and enemy systems work together
    if (PowerupSystem && EnemyManager)
    {
        AActor* TestActor = TestWorld->SpawnActor<AActor>();
        bool bPowerupApplied = PowerupSystem->ApplyPowerup(TEXT("Powerup_0"), TestActor);
        TestTrue("Should apply powerup", bPowerupApplied);
        
        EnemyManager->SpawnWallTurrets(2);
        TestTrue("Should spawn enemies", EnemyManager->GetActiveEnemies().Num() > 0);
    }

    // CLEANUP
    TestWorld->DestroyWorld(false);
    return true;
}

/**
 * PERFORMANCE INTEGRATION TEST
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_PerformanceIntegration, 
    "SnakeRogue.Integration.Performance",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_PerformanceIntegration::RunTest(const FString& Parameters)
{
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // Spawn all systems
    ALevelGenActor* LevelGen = TestWorld->SpawnActor<ALevelGenActor>();
    APowerupSystemActor* PowerupSystem = TestWorld->SpawnActor<APowerupSystemActor>();
    AEnemyManagerActor* EnemyManager = TestWorld->SpawnActor<AEnemyManagerActor>();

    if (LevelGen && PowerupSystem && EnemyManager)
    {
        // Stress test: Multiple arena expansions
        for (int32 i = 0; i < 10; ++i)
        {
            LevelGen->ForceArenaExpansion();
        }
        
        // Stress test: Multiple powerup applications
        AActor* TestActor = TestWorld->SpawnActor<AActor>();
        for (int32 i = 0; i < 20; ++i)
        {
            FString PowerupID = FString::Printf(TEXT("Powerup_%d"), i % 10);
            PowerupSystem->ApplyPowerup(PowerupID, TestActor);
        }
        
        // Stress test: Enemy spawning
        EnemyManager->SpawnWallTurrets(10);
        EnemyManager->SpawnChasers(5);
        
        TestTrue("Performance test completed", true);
    }

    TestWorld->DestroyWorld(false);
    return true;
}

#endif // WITH_AUTOMATION_TESTS
