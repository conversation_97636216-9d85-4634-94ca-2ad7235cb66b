# Snake Rogue - Setup Instructions

## Git Repository Setup

Since Git is not currently available in the development environment, please follow these manual steps:

### 1. Install Git (if not already installed)
- Download Git from https://git-scm.com/download/win
- Install with default settings
- Restart your terminal/IDE

### 2. Initialize Local Repository
```bash
cd "d:\Projects\VS Code - Augment\snake roguelike"
git init
git add .
git commit -m "chore: initial project skeleton"
```

### 3. Create GitHub Repository
1. Go to https://github.com/new
2. Repository name: `snake-roguelike`
3. Description: `Snake Rogue - A fully 3D, isometric, procedurally expanding roguelike built in Unreal Engine 5.4`
4. Set to **Private**
5. Do NOT initialize with README, .gitignore, or license (already created)
6. Click "Create repository"

### 4. Connect Local to Remote
```bash
git remote add origin https://github.com/Rolaand-Jayz/snake-roguelike.git
git branch -M main
git push -u origin main
```

### 5. Enable Branch Protection
1. Go to repository Settings → Branches
2. Add rule for `main` branch
3. Enable "Require pull request reviews before merging"
4. Enable "Require status checks to pass before merging"

## Git LFS Setup (for Unreal Engine assets)
```bash
git lfs install
git lfs track "*.uasset"
git lfs track "*.umap"
git lfs track "*.fbx"
git lfs track "*.png"
git lfs track "*.jpg"
git lfs track "*.wav"
git lfs track "*.mp3"
git add .gitattributes
git commit -m "feat: configure Git LFS for Unreal Engine assets"
git push
```

## Development Workflow
1. **Before starting work**: `git pull --rebase origin main`
2. **Create feature branch**: `git checkout -b feature/task-name`
3. **Make changes and commit**: `git add . && git commit -m "feat: description"`
4. **Push branch**: `git push origin feature/task-name`
5. **Create Pull Request** on GitHub
6. **After merge**: `git checkout main && git pull --rebase origin main`

## Project Structure Verification

After setup, your project should have this structure:
```
snake roguelike/
├── docs/
│   ├── Asset_Resources.md
│   ├── Debugging_Strategy.md
│   ├── Feature_Matrix.md
│   ├── Game_Design_Document.md
│   ├── Tasks_Beginner_Friendly.md
│   ├── Technical_Spec.md
│   ├── Testing_Strategy.md
│   └── User_Stories_and_Flows.md
├── plans/
│   ├── detailed_plan.md
│   └── atomic_tasks_checklist.md
├── .gitignore
└── SETUP_INSTRUCTIONS.md
```

## Next Steps
1. Complete Git setup following instructions above
2. Install Unreal Engine 5.4
3. Begin with Task T-001: Create UE 5.4 project with C++ template
4. Follow the atomic tasks checklist in order of dependencies

## MCP Tools Integration
Once the repository is set up, the following MCP tools should be configured:
- `resolve-library-id` for UE5.4 API documentation
- `get-library-docs` for detailed API references
- `unreal-insights-mcp` for performance profiling
- `cpp-analysis-mcp` for code quality analysis
- `git-workflow-mcp` for automated Git operations

## Support
If you encounter issues during setup, refer to:
- Unreal Engine Documentation: https://docs.unrealengine.com/5.4/
- Git Documentation: https://git-scm.com/doc
- GitHub Documentation: https://docs.github.com/
