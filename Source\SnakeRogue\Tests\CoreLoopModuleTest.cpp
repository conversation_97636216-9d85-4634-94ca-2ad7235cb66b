// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreMinimal.h"
#include "Misc/AutomationTest.h"
#include "Tests/AutomationCommon.h"
#include "Engine/World.h"
#include "GameFramework/GameStateBase.h"
#include "CoreLoopGameState.h"

#if WITH_AUTOMATION_TESTS

/**
 * Test_CoreLoopModule_ShouldInitializeCorrectly
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the CoreLoop module initializes correctly as a singleton UGameState.
 * Following TDD Red-Green-Refactor cycle:
 * 1. RED: Write failing test that describes desired behavior
 * 2. GREEN: Write minimal code to make test pass
 * 3. REFACTOR: Clean up code while maintaining green tests
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_CoreLoopModule_ShouldInitializeCorrectly, 
    "SnakeRogue.CoreLoop.InitializeCorrectly",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_CoreLoopModule_ShouldInitializeCorrectly::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT: Attempt to get CoreLoop module (this should now pass)
    ACoreLoopGameState* CoreLoopGameState = ACoreLoopGameState::GetCoreLoopGameState(TestWorld);
    
    // ASSERT: Verify CoreLoop module behavior
    // This test should now PASS because CoreLoop module is implemented
    TestTrue("CoreLoop GameState should exist", CoreLoopGameState != nullptr);

    if (CoreLoopGameState)
    {
        // Test singleton behavior
        ACoreLoopGameState* SecondInstance = ACoreLoopGameState::GetCoreLoopGameState(TestWorld);
        TestEqual("CoreLoop should be singleton", CoreLoopGameState, SecondInstance);

        // Test initialization state
        TestTrue("CoreLoop should be initialized", CoreLoopGameState->AreCoreSystemsInitialized());

        // Test game flow state
        TestEqual("CoreLoop should start in Menu state", CoreLoopGameState->GetGameFlowState(), EGameFlowState::Menu);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_CoreLoopModule_ShouldManageGameFlow
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the CoreLoop module can manage basic game flow states.
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_CoreLoopModule_ShouldManageGameFlow, 
    "SnakeRogue.CoreLoop.ManageGameFlow",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_CoreLoopModule_ShouldManageGameFlow::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test game flow management
    // This should now pass because CoreLoop module is implemented
    ACoreLoopGameState* CoreLoopGameState = ACoreLoopGameState::GetCoreLoopGameState(TestWorld);
    TestTrue("CoreLoop GameState should exist for flow management", CoreLoopGameState != nullptr);

    if (CoreLoopGameState)
    {
        // Test game state transitions
        TestEqual("Should start in Menu state", CoreLoopGameState->GetGameFlowState(), EGameFlowState::Menu);

        CoreLoopGameState->SetGameFlowState(EGameFlowState::Playing);
        TestEqual("Should transition to Playing state", CoreLoopGameState->GetGameFlowState(), EGameFlowState::Playing);

        CoreLoopGameState->SetGameFlowState(EGameFlowState::Paused);
        TestEqual("Should transition to Paused state", CoreLoopGameState->GetGameFlowState(), EGameFlowState::Paused);

        CoreLoopGameState->SetGameFlowState(EGameFlowState::GameOver);
        TestEqual("Should transition to GameOver state", CoreLoopGameState->GetGameFlowState(), EGameFlowState::GameOver);

        // Test basic functionality
        TestTrue("CoreLoop should handle basic game state", CoreLoopGameState->GetWorld() == TestWorld);
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

/**
 * Test_CoreLoopModule_ShouldHandleModuleCommunication
 * 
 * RED PHASE: This test should FAIL initially
 * 
 * Tests that the CoreLoop module can communicate with other modules.
 */
IMPLEMENT_SIMPLE_AUTOMATION_TEST(FTest_CoreLoopModule_ShouldHandleModuleCommunication, 
    "SnakeRogue.CoreLoop.HandleModuleCommunication",
    EAutomationTestFlags::ApplicationContextMask | EAutomationTestFlags::ProductFilter)

bool FTest_CoreLoopModule_ShouldHandleModuleCommunication::RunTest(const FString& Parameters)
{
    // ARRANGE: Set up test environment
    UWorld* TestWorld = UWorld::CreateWorld(EWorldType::Game, false);
    TestTrue("Test world should be created", TestWorld != nullptr);
    
    if (!TestWorld)
    {
        return false;
    }

    // ACT & ASSERT: Test module communication
    // This should now pass because CoreLoop module is implemented
    ACoreLoopGameState* CoreLoopGameState = ACoreLoopGameState::GetCoreLoopGameState(TestWorld);
    TestTrue("CoreLoop GameState should exist for communication", CoreLoopGameState != nullptr);

    if (CoreLoopGameState)
    {
        // Test that the module can be accessed and is valid
        TestTrue("CoreLoop should be accessible for communication", IsValid(CoreLoopGameState));

        // Test core systems initialization (prerequisite for module communication)
        TestTrue("CoreLoop should have core systems initialized", CoreLoopGameState->AreCoreSystemsInitialized());

        // TODO: Test communication with other modules when implemented:
        // - CameraRig module
        // - LevelGen module
        // - PowerupSystem module
        // - EnemyManager module
        // - NetSync module
    }

    // CLEANUP: Destroy test world
    TestWorld->DestroyWorld(false);
    
    return true;
}

#endif // WITH_AUTOMATION_TESTS
