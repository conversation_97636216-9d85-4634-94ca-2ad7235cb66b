@echo off
echo ========================================
echo    SNAKE ROGUE - UE5.6 FIXED LAUNCH
echo ========================================
echo.

echo [INFO] Launching with UE5.6 compatibility fixes...
echo.

REM Clean up any existing project files
echo [CLEANUP] Removing old project files...
if exist "Binaries" (
    echo   Removing Binaries folder...
    rmdir /s /q "Binaries" 2>nul
)

if exist "Intermediate" (
    echo   Removing Intermediate folder...
    rmdir /s /q "Intermediate" 2>nul
)

if exist ".vs" (
    echo   Removing Visual Studio cache...
    rmdir /s /q ".vs" 2>nul
)

if exist "*.sln" (
    echo   Removing solution files...
    del "*.sln" 2>nul
)

echo   ✅ Cleanup complete!
echo.

REM Generate fresh project files
echo [GENERATE] Creating fresh UE5.6 project files...

REM Try to find UE5.6 installation
set UE_PATH=""
if exist "C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe" (
    set UE_PATH="C:\Program Files\Epic Games\UE_5.6"
    echo   Found UE5.6 at: %UE_PATH%
) else (
    echo   Searching for UE5.6 installation...
    for /d %%i in ("C:\Program Files\Epic Games\UE_5.*") do (
        if exist "%%i\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe" (
            set UE_PATH="%%i"
            echo   Found UE at: %%i
            goto :found
        )
    )
)

:found
if %UE_PATH%=="" (
    echo   ❌ UE5.6 not found! Please install Unreal Engine 5.6
    pause
    exit /b 1
)

echo   ✅ UE5.6 found!
echo.

REM Generate project files
echo [GENERATE] Generating Visual Studio project files...
%UE_PATH%\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.exe -projectfiles -project="%CD%\SnakeRogue.uproject" -game -rocket -progress

if %ERRORLEVEL% EQU 0 (
    echo   ✅ Project files generated successfully!
) else (
    echo   ❌ Failed to generate project files
    pause
    exit /b 1
)

echo.

REM Launch UE5.6
echo [LAUNCH] Starting Unreal Engine 5.6...
%UE_PATH%\Engine\Binaries\Win64\UnrealEditor.exe "%CD%\SnakeRogue.uproject"

echo.
echo [INFO] UE5.6 launched! Check for any remaining compilation issues.
echo.
echo If you still get errors:
echo   1. In UE5.6: File → Refresh Visual Studio Project
echo   2. Build → Compile SnakeRogue
echo   3. Check Output Log for specific errors
echo.
pause
