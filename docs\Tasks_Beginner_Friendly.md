# Beginner‑Friendly Task List – **<PERSON> Rogue**

_Tip: **<PERSON><PERSON><PERSON>** is italicized then explained right after._

## Milestone 1 – Core Prototype
1. **Fork** the repo _(fork = make your own copy on GitHub)_.
2. Build UE project → open `SnakeRogue.uproject`.
3. Implement basic *tile‑snap* movement _(tile‑snap = fixed grid steps)_.
4. Spawn one **buff** _(buff = good power‑up)_ and one **debuff** _(debuff = bad effect)_.

## Milestone 2 – Arena Expansion
1. Write `ArenaExpander.cpp` – adds tiles every 4 s.
2. Add camera spring‑arm.

## Milestone 3 – Power‑Up System
1. Create data table `DT_Powerups`.
2. Hook UI icon display.

## Milestone 4 – First Publish
1. Push to GitHub.
2. Run CI → itch.io draft.

---
